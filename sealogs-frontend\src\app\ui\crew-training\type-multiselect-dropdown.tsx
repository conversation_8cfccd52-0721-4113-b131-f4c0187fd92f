import { useEffect, useState } from 'react'
import { getTrainingTypes } from '@/app/lib/actions'
import { isEmpty } from 'lodash'
import TrainingTypeModel from '@/app/offline/models/trainingType'
import { Combobox, Option } from '@/components/ui/comboBox'
import { useLazyQuery } from '@apollo/client'
import { CREW_TRAINING_TYPES } from '@/app/lib/graphQL/query'

const TrainingTypeMultiSelectDropdown = ({
    value = [],
    onChange,
    locked,
    offline = false,
}: any) => {
    const [typesOfTraining, setTypesOfTraining] = useState<any>([])
    const [selectedTrainings, setSelectedTrainings] = useState<any>([])
    const limit = 100 // Default limit from the query
    const [currentOffset, setCurrentOffset] = useState(0)
    const [hasMorePages, setHasMorePages] = useState(true)
    const [isLoadingMore, setIsLoadingMore] = useState(false)
    const trainingTypeModel = new TrainingTypeModel()
    const handleSetTrainingTypes = (data: any) => {
        const formattedData = data.map((trainingType: any) => ({
            value: trainingType.id,
            label: trainingType.title,
        }))
        formattedData.sort((a: any, b: any) => a.label.localeCompare(b.label))
        setTypesOfTraining(formattedData)
        const selectedData = value.map((value: any) => {
            return formattedData.find((type: any) => type.value === value)
        })
        setSelectedTrainings(selectedData)
    }

    const [queryTrainingTypes] = useLazyQuery(CREW_TRAINING_TYPES, {
        fetchPolicy: 'cache-and-network',
        onCompleted: (response: any) => {
            const data = response.readTrainingTypes.nodes
            const pageInfo = response.readTrainingTypes.pageInfo
            if (data) {
                const formattedData = data.map((trainingType: any) => ({
                    value: trainingType.id,
                    label: trainingType.title,
                }))
                const updatedTrainingTypes = [
                    ...typesOfTraining,
                    ...formattedData,
                ]
                setTypesOfTraining(updatedTrainingTypes)
                if (pageInfo.hasNextPage) {
                    // Prepare for next page
                    const nextOffset = currentOffset + limit
                    setCurrentOffset(nextOffset)

                    // Load next page
                    setIsLoadingMore(true)
                    queryTrainingTypes({
                        variables: {
                            limit: limit,
                            offset: nextOffset,
                        },
                    })
                } else {
                    setHasMorePages(false)
                    setIsLoadingMore(false)
                }
            }
        },
        onError: (error: any) => {
            console.error('queryTrainingTypes error', error)
            setIsLoadingMore(false)
        },
    })
    const loadTrainingTypes = async () => {
        setTypesOfTraining([])
        setCurrentOffset(0)
        setHasMorePages(true)
        setIsLoadingMore(true)
        await queryTrainingTypes({
            variables: {
                limit: limit,
                offset: 0,
            },
        })
    }

    useEffect(() => {
        if (!isLoadingMore) {
            let formattedData = [...typesOfTraining]
            formattedData.sort((a: any, b: any) =>
                a.label.localeCompare(b.label),
            )
            setTypesOfTraining(formattedData)
            const selectedData = value.map((value: any) => {
                return formattedData.find((type: any) => type.value === value)
            })
            setSelectedTrainings(selectedData)
        }
    }, [isLoadingMore])

    useEffect(() => {
        if (offline) {
            trainingTypeModel.getAll().then((data: any) => {
                handleSetTrainingTypes(data)
            })
        }
    }, [offline])

    const handleOnChange = (value: Option | Option[] | null) => {
        // Handle case when all options are deselected (value is null or empty array)
        if (!value || (Array.isArray(value) && value.length === 0)) {
            setSelectedTrainings([])
            onChange([])
            return
        }

        const selectedValues = Array.isArray(value) ? value : [value]

        setSelectedTrainings(selectedValues)
        onChange(selectedValues)
    }

    useEffect(() => {
        if (!isEmpty(value) && !isEmpty(typesOfTraining)) {
            const selectedData = value.map((value: any) => {
                return typesOfTraining.find((type: any) => type.value === value)
            })
            setSelectedTrainings(selectedData)
        }
    }, [value, typesOfTraining])

    useEffect(() => {
        if (!offline) {
            loadTrainingTypes()
        }
    }, [])

    return (
        <Combobox
            disabled={locked}
            value={selectedTrainings}
            options={typesOfTraining}
            onChange={handleOnChange}
            isLoading={!typesOfTraining}
            buttonClassName="w-full"
            labelClassName="w-full"
            responsiveBadges
            placeholder="Select training type"
            multi
        />
    )
}

export default TrainingTypeMultiSelectDropdown
