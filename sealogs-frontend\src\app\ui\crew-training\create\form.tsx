'use client'

import dayjs from 'dayjs'
import { isEmpty } from 'lodash'
import { useEffect, useState } from 'react'
import CrewDropdown from '../../../../components/filter/components/crew-dropdown/crew-dropdown'
import { TrainingSessionFormSkeleton } from '../../../../components/skeletons'
import TrainingTypeMultiSelectDropdown from '../type-multiselect-dropdown'
import CrewMultiSelectDropdown from '../../crew/multiselect-dropdown/multiselect-dropdown'
import {
    CREATE_TRAINING_SESSION,
    UPDATE_TRAINING_SESSION,
    CREATE_MEMBER_TRAINING_SIGNATURE,
    UPDATE_MEMBER_TRAINING_SIGNATURE,
    CREATE_TRAINING_SESSION_DUE,
    UPDATE_TRAINING_SESSION_DUE,
    CREATE_CUSTOMISED_COMPONENT_FIELD_DATA,
    UPDATE_CUSTOMISED_COMPONENT_FIELD_DATA,
} from '@/app/lib/graphQL/mutation'
import {
    GET_MEMBER_TRAINING_SIGNATURES,
    GET_SECTION_MEMBER_IMAGES,
    READ_ONE_TRAINING_SESSION_DUE,
} from '@/app/lib/graphQL/query'
import { useMutation, useLazyQuery } from '@apollo/client'
import { useRouter } from 'next/navigation'
import {
    getTrainingSessionByID,
    getTrainingTypeByID,
    getTrainingTypes,
} from '@/app/lib/actions'
import Editor from '../../editor'
import { ScrollArea } from '@/components/ui/scroll-area'
import { getPermissions, hasPermission } from '@/app/helpers/userHelper'
import Loading from '@/app/loading'
import { Label } from '@/components/ui/label'
import { Combobox, Option } from '@/components/ui/comboBox'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Separator } from '@/components/ui/separator'
import {
    Sheet,
    SheetContent,
    SheetHeader,
    SheetTitle,
} from '@/components/ui/sheet'
import { Button } from '@/components/ui/button'
import SignaturePad from '@/components/signature-pad'
import { FooterWrapper } from '@/components/footer-wrapper'
import SeaLogsButton from '@/components/ui/sea-logs-button'
import {
    CheckField,
    CheckFieldContent,
    DailyCheckField,
} from '@/components/daily-check-field'
import DatePicker from '@/components/DateRange'
import { useToast } from '@/hooks/use-toast'
import { ArrowLeft } from 'lucide-react'
import { H4 } from '@/components/ui'
import {
    AlertDialog,
    AlertDialogAction,
    AlertDialogCancel,
    AlertDialogContent,
    AlertDialogDescription,
    AlertDialogFooter,
    AlertDialogHeader,
    AlertDialogTitle,
} from '@/components/ui/alert-dialog'
import { Textarea } from '@/components/ui/textarea'
import { ReadVessels } from './queries'

const TrainingForm = ({
    trainingID = 0,
    memberId = 0,
    trainingTypeId = 0,
    vesselId = 0,
}: {
    trainingID: number
    memberId: number
    trainingTypeId: number
    vesselId?: number
}) => {
    const router = useRouter()
    const { toast } = useToast()
    const [isLoading, setIsLoading] = useState(true)
    const [training, setTraining] = useState<any>({})
    const [rawTraining, setRawTraining] = useState<any>()
    const [trainingDate, setTrainingDate] = useState(new Date())
    const [hasFormErrors, setHasFormErrors] = useState(false)
    const [selectedMemberList, setSelectedMemberList] = useState([] as any[])
    const [signatureMembers, setSignatureMembers] = useState([] as any[])
    const [vessels, setVessels] = useState<any>()
    const [trainingTypes, setTrainingTypes] = useState<any>([])
    const [content, setContent] = useState<any>('')
    const [openViewProcedure, setOpenViewProcedure] = useState(false)
    const [currentComment, setCurrentComment] = useState<any>('')
    const [currentField, setCurrentField] = useState<any>('')
    const [bufferProcedureCheck, setBufferProcedureCheck] = useState<any>([])
    const [bufferFieldComment, setBufferFieldComment] = useState<any>([])
    const [openCommentAlert, setOpenCommentAlert] = useState(false)
    const [formErrors, setFormErrors] = useState({
        TrainingTypes: '',
        TrainerID: '',
        VesselID: '',
        Date: '',
    })
    const [vesselID, setVesselID] = useState(vesselId)
    const [fieldImages, setFieldImages] = useState<any>(false)

    getTrainingTypes(setTrainingTypes)

    const [getFieldImages] = useLazyQuery(GET_SECTION_MEMBER_IMAGES, {
        fetchPolicy: 'cache-and-network',
        onCompleted: (response: any) => {
            const data = response.readCaptureImages.nodes
            if (data) {
                setFieldImages(data)
            }
        },
        onError: (error: any) => {
            console.error('getFieldImages error', error)
        },
    })

    useEffect(() => {
        getFieldImages({
            variables: {
                filter: {
                    trainingSessionID: { eq: trainingID },
                },
            },
        })
    }, [])

    const refreshImages = async () => {
        await getFieldImages({
            variables: {
                filter: {
                    trainingSessionID: { eq: trainingID },
                },
            },
        })
    }

    const handleSetTraining = (training: any) => {
        const tDate = new Date(training.date)
        setTrainingDate(tDate)
        const trainingData = {
            ID: trainingID,
            Date: dayjs(training.date).format('YYYY-MM-DD'),
            Members: training.members.nodes.map((m: any) => m.id),
            TrainerID: training.trainer.id,
            TrainingSummary: training.trainingSummary,
            TrainingTypes: training.trainingTypes.nodes.map((t: any) => t.id),
            // VesselID: training.vessel.id,
            VesselID: training.vesselID,
        }
        setRawTraining(training)
        setTraining(trainingData)
        setContent(training.trainingSummary)

        const members =
            training.members.nodes.map((m: any) => ({
                label: `${m.firstName ?? ''} ${m.surname ?? ''}`,
                value: m.id,
            })) || []

        const vesselCrewIds = training.vessel.seaLogsMembers.nodes.map(
            (slm: any) => +slm.id,
        )
        const vesselCrews = members.filter((m: any) =>
            vesselCrewIds.includes(+m.value),
        )

        setSelectedMemberList(vesselCrews)
        const signatures = training.signatures.nodes.map((s: any) => ({
            MemberID: s.member.id,
            SignatureData: s.signatureData,
            ID: s.id,
        }))
        setSignatureMembers(signatures)

        // Initialize buffer with existing procedure field data for updates
        if (training.procedureFields?.nodes) {
            const existingProcedureChecks = training.procedureFields.nodes.map(
                (field: any) => ({
                    fieldId: field.customisedComponentFieldID,
                    status: field.status === 'Ok',
                }),
            )
            setBufferProcedureCheck(existingProcedureChecks)

            const existingFieldComments = training.procedureFields.nodes
                .filter((field: any) => field.comment)
                .map((field: any) => ({
                    fieldId: field.customisedComponentFieldID,
                    comment: field.comment,
                }))
            setBufferFieldComment(existingFieldComments)
        }
    }

    const handleSetVessels = (data: any) => {
        const activeVessels = data?.filter((vessel: any) => !vessel.archived)
        const formattedData = [
            {
                label: 'Other',
                value: 'Other',
            },
            {
                label: 'Desktop/shore',
                value: 'Onshore',
            },
            ...activeVessels.map((vessel: any) => ({
                value: vessel.id,
                label: vessel.title,
            })),
        ]
        setVessels(formattedData)
    }

    const [queryVessels] = useLazyQuery(ReadVessels, {
        fetchPolicy: 'cache-and-network',
        onCompleted: (queryVesselResponse: any) => {
            if (queryVesselResponse.readVessels.nodes) {
                handleSetVessels(queryVesselResponse.readVessels.nodes)
            }
        },
        onError: (error: any) => {
            console.error('queryVessels error', error)
        },
    })
    const loadVessels = async () => {
        await queryVessels({
            variables: {
                limit: 200,
                offset: 0,
            },
        })
    }
    useEffect(() => {
        if (isLoading) {
            loadVessels()
            setIsLoading(false)
        }
    }, [isLoading])

    getTrainingSessionByID(trainingID, handleSetTraining)

    const [
        mutationCreateTrainingSession,
        { loading: mutationCreateTrainingSessionLoading },
    ] = useMutation(CREATE_TRAINING_SESSION, {
        onCompleted: (response: any) => {
            const data = response.createTrainingSession
            if (data.id > 0) {
                if (bufferProcedureCheck.length > 0) {
                    const procedureFields = bufferProcedureCheck.map(
                        (procedureField: any) => {
                            return {
                                status: procedureField.status ? 'Ok' : 'Not_Ok',
                                trainingSessionID: data.id,
                                customisedComponentFieldID:
                                    procedureField.fieldId,
                                comment: bufferFieldComment.find(
                                    (comment: any) =>
                                        comment.fieldId ==
                                        procedureField.fieldId,
                                )?.comment,
                            }
                        },
                    )
                    procedureFields.forEach((procedureField: any) => {
                        createCustomisedComponentFieldData({
                            variables: {
                                input: procedureField,
                            },
                        })
                    })
                }
                updateTrainingSessionDues()
                updateSignatures(data.id)
                handleEditorChange(data.trainingSummary)
                router.push('/crew-training')
            } else {
                console.error('mutationCreateUser error', response)
            }
        },
        onError: (error: any) => {
            console.error('mutationCreateTrainingSession error', error)
        },
    })
    const [
        mutationUpdateTrainingSession,
        { loading: mutationUpdateTrainingSessionLoading },
    ] = useMutation(UPDATE_TRAINING_SESSION, {
        onCompleted: (response: any) => {
            const data = response.updateTrainingSession
            if (data.id > 0) {
                // Handle procedure checks for updates
                if (bufferProcedureCheck.length > 0) {
                    const procedureFields = bufferProcedureCheck.map(
                        (procedureField: any) => {
                            const existingField =
                                rawTraining?.procedureFields?.nodes?.find(
                                    (field: any) =>
                                        field.customisedComponentFieldID ===
                                        procedureField.fieldId,
                                )

                            return {
                                id: existingField?.id, // Include ID for updates
                                status: procedureField.status ? 'Ok' : 'Not_Ok',
                                trainingSessionID: data.id,
                                customisedComponentFieldID:
                                    procedureField.fieldId,
                                comment: bufferFieldComment.find(
                                    (comment: any) =>
                                        comment.fieldId ==
                                        procedureField.fieldId,
                                )?.comment,
                            }
                        },
                    )
                    procedureFields.forEach((procedureField: any) => {
                        if (procedureField.id) {
                            // Update existing field
                            updateCustomisedComponentFieldData({
                                variables: {
                                    input: procedureField,
                                },
                            })
                        } else {
                            // Create new field
                            const { id, ...createInput } = procedureField
                            createCustomisedComponentFieldData({
                                variables: {
                                    input: createInput,
                                },
                            })
                        }
                    })
                }
                updateTrainingSessionDues()
                updateSignatures(trainingID)
                handleEditorChange(data.trainingSummary)
                if (+memberId > 0) {
                    router.push(`/crew/info?id=${memberId}`)
                } else if (+vesselId > 0) {
                    router.push(`/vessel/info?id=${vesselId}`)
                } else {
                    router.push('/crew-training')
                }
            } else {
                console.error('mutationUpdateTrainingSession error', response)
            }
        },
        onError: (error: any) => {
            console.error('mutationUpdateTrainingSession error', error)
        },
    })
    const [
        readOneTrainingSessionDue,
        { loading: readOneTrainingSessionDueLoading },
    ] = useLazyQuery(READ_ONE_TRAINING_SESSION_DUE, {
        fetchPolicy: 'cache-and-network',
        onCompleted: (response: any) => {
            return response.readOneTrainingSessionDue.data
        },
        onError: (error: any) => {
            console.error('readOneTrainingSessionDueLoading error:', error)
            return null
        },
    })
    const getTrainingSessionDueWithVariables = async (
        variables: any = {},
        onCompleted: any,
    ) => {
        const { data }: any = await readOneTrainingSessionDue({
            variables: variables,
        })
        onCompleted(data.readOneTrainingSessionDue)
    }

    const [
        mutationCreateTrainingSessionDue,
        { loading: createTrainingSessionDueLoading },
    ] = useMutation(CREATE_TRAINING_SESSION_DUE, {
        onCompleted: (response: any) => {},
        onError: (error: any) => {
            console.error('createTrainingSessionDue error', error)
        },
    })
    const [
        mutationUpdateTrainingSessionDue,
        { loading: updateTrainingSessionDueLoading },
    ] = useMutation(UPDATE_TRAINING_SESSION_DUE, {
        onCompleted: (response: any) => {},
        onError: (error: any) => {
            console.error('updateTrainingSessionDue error', error)
        },
    })
    const updateTrainingSessionDues = async () => {
        const trainingSessionDues: any = []
        const vesselID = training.VesselID
        training.TrainingTypes.forEach((t: any) => {
            const trainingInfo = trainingTypes.find((tt: any) => tt.id === t)

            if (!isEmpty(trainingInfo) && trainingInfo.occursEvery > 0) {
                const trainingTypeID = t
                const newDueDate = dayjs(training.Date).add(
                    trainingInfo.occursEvery,
                    'day',
                )
                training.Members.forEach((m: any) => {
                    const memberID = m
                    trainingSessionDues.push({
                        dueDate: newDueDate.format('YYYY-MM-DD'),
                        memberID: memberID,
                        vesselID: vesselID,
                        trainingTypeID: trainingTypeID,
                    })
                })
            }
        })
        let trainingSessionDueWithIDs: any = []
        if (!isEmpty(trainingSessionDues)) {
            await Promise.all(
                trainingSessionDues.map(async (item: any) => {
                    const variables = {
                        filter: {
                            memberID: {
                                eq: item.memberID,
                            },
                            vesselID: {
                                eq: item.vesselID,
                            },
                            trainingTypeID: {
                                eq: item.trainingTypeID,
                            },
                        },
                    }
                    const onCompleted = (response: any) => {
                        trainingSessionDueWithIDs.push({
                            ...item,
                            id: response?.id ?? 0,
                        })
                    }

                    await getTrainingSessionDueWithVariables(
                        variables,
                        onCompleted,
                    )
                }),
            )
        }

        if (!isEmpty(trainingSessionDueWithIDs)) {
            await Promise.all(
                Array.from(trainingSessionDueWithIDs).map(async (item: any) => {
                    const variables = {
                        variables: { input: item },
                    }
                    if (item.id === 0) {
                        await mutationCreateTrainingSessionDue(variables)
                    } else {
                        await mutationUpdateTrainingSessionDue(variables)
                    }
                }),
            )
        }
    }

    const [createCustomisedComponentFieldData] = useMutation(
        CREATE_CUSTOMISED_COMPONENT_FIELD_DATA,
        {
            onCompleted: (response: any) => {
                const data = response.createCustomisedComponentFieldData
                if (data.id > 0 && rawTraining?.procedureFields?.nodes) {
                    setRawTraining({
                        ...rawTraining,
                        procedureFields: {
                            ...rawTraining.procedureFields,
                            nodes: [...rawTraining.procedureFields.nodes, data],
                        },
                    })
                } else {
                    console.error(
                        'createCustomisedComponentFieldData error',
                        response,
                    )
                }
            },
            onError: (error: any) => {
                console.error('createCustomisedComponentFieldData error', error)
            },
        },
    )

    const [updateCustomisedComponentFieldData] = useMutation(
        UPDATE_CUSTOMISED_COMPONENT_FIELD_DATA,
        {
            onCompleted: (response: any) => {
                const data = response.updateCustomisedComponentFieldData
                if (data.id > 0) {
                    setRawTraining({
                        ...rawTraining,
                        procedureFields: {
                            ...rawTraining.procedureFields,
                            nodes: [
                                ...rawTraining?.procedureFields?.nodes.filter(
                                    (procedureField: any) =>
                                        procedureField.customisedComponentFieldID !==
                                        data.customisedComponentFieldID,
                                ),
                                {
                                    ...data,
                                },
                            ],
                        },
                    })
                } else {
                    console.error(
                        'updateCustomisedComponentFieldData error',
                        response,
                    )
                }
            },
            onError: (error: any) => {
                console.error('updateCustomisedComponentFieldData error', error)
            },
        },
    )

    const handleSave = async () => {
        let hasErrors = false
        let errors = {
            TrainingTypes: '',
            TrainerID: '',
            VesselID: '',
            Date: '',
        }
        setFormErrors(errors)
        if (isEmpty(training.TrainingTypes)) {
            hasErrors = true
            errors.TrainingTypes = 'Nature of training is required'
        }
        if (!(training.TrainerID && training.TrainerID > 0)) {
            hasErrors = true
            errors.TrainerID = 'Trainer is required'
        }
        if (
            !training.VesselID &&
            !(training.TrainingLocationID && training.TrainingLocationID >= 0)
        ) {
            hasErrors = true
            errors.VesselID = 'Location is required'
        }

        if (typeof training.Date === 'undefined') {
            training.Date = dayjs().format('YYYY-MM-DD')
        }

        if (training.Date === null || !dayjs(training.Date).isValid()) {
            hasErrors = true
            errors.Date = 'The date is invalid'
        }
        if (hasErrors) {
            setHasFormErrors(true)
            setFormErrors(errors)
            toast({
                title: 'Error',
                description:
                    errors.TrainingTypes ||
                    errors.TrainerID ||
                    errors.VesselID ||
                    errors.Date,
                variant: 'destructive',
            })
            return
        }
        const input = {
            id: trainingID,
            date: training.Date
                ? dayjs(training.Date).format('YYYY-MM-DD')
                : '',
            members: training.Members?.join(','),
            trainerID: training.TrainerID,
            trainingSummary: content,
            trainingTypes: training.TrainingTypes?.join(','),
            vesselID: training?.VesselID,
            trainingLocationType: training?.VesselID
                ? training.VesselID === 'Other' ||
                  training.VesselID === 'Onshore'
                    ? training.VesselID
                    : 'Vessel'
                : 'Location',
        }
        if (trainingID === 0) {
            await mutationCreateTrainingSession({
                variables: {
                    input: input,
                },
            })
        } else {
            await mutationUpdateTrainingSession({
                variables: {
                    input: input,
                },
            })
        }
    }
    // var signatureCount = 0

    const updateSignatures = (TrainingID: number) => {
        signatureMembers.length > 0 &&
            signatureMembers?.forEach((signature: any) => {
                checkAndSaveSignature(signature, TrainingID)
            })
    }

    const checkAndSaveSignature = async (
        signature: any,
        TrainingID: number,
    ) => {
        await queryGetMemberTrainingSignatures({
            variables: {
                filter: {
                    memberID: { eq: signature.MemberID },
                    trainingSessionID: { in: TrainingID },
                },
            },
        })
            .then((response: any) => {
                const data = response.data.readMemberTraining_Signatures.nodes
                if (data.length > 0) {
                    mutationUpdateMemberTrainingSignature({
                        variables: {
                            input: {
                                id: data[0].id,
                                memberID: signature.MemberID,
                                signatureData: signature.SignatureData,
                                trainingSessionID: TrainingID,
                            },
                        },
                    })
                } else {
                    if (signature.SignatureData) {
                        mutationCreateMemberTrainingSignature({
                            variables: {
                                input: {
                                    memberID: signature.MemberID,
                                    signatureData: signature.SignatureData,
                                    trainingSessionID: TrainingID,
                                },
                            },
                        })
                    }
                }
            })
            .catch((error: any) => {
                console.error(
                    'mutationGetMemberTrainingSignatures error',
                    error,
                )
            })
    }

    const [queryGetMemberTrainingSignatures] = useLazyQuery(
        GET_MEMBER_TRAINING_SIGNATURES,
    )

    const [
        mutationUpdateMemberTrainingSignature,
        { loading: mutationUpdateMemberTrainingSignatureLoading },
    ] = useMutation(UPDATE_MEMBER_TRAINING_SIGNATURE, {
        onCompleted: (response: any) => {
            const data = response.updateMemberTraining_Signature
            if (data.id > 0) {
                // signatureCount++
                // if (signatureCount === signatureMembers.length) {
                // }
            } else {
                console.error(
                    'mutationUpdateMemberTrainingSignature error',
                    response,
                )
            }
        },
        onError: (error: any) => {
            console.error('mutationUpdateMemberTrainingSignature error', error)
        },
    })

    const [
        mutationCreateMemberTrainingSignature,
        { loading: mutationCreateMemberTrainingSignatureLoading },
    ] = useMutation(CREATE_MEMBER_TRAINING_SIGNATURE, {
        onCompleted: (response: any) => {
            const data = response.createMemberTraining_Signature
            if (data.id > 0) {
                // signatureCount++
                // if (signatureCount === signatureMembers.length) {
                // }
            } else {
                console.error(
                    'mutationCreateMemberTrainingSignature error',
                    response,
                )
            }
        },
        onError: (error: any) => {
            console.error('mutationCreateMemberTrainingSignature error', error)
        },
    })

    const handleTrainingDateChange = (date: any) => {
        setTrainingDate(date && new Date(date.toString()))
        setTraining({
            ...training,
            Date: dayjs(date).format('YYYY-MM-DD'),
        })
    }

    const handleTrainerChange = (trainer: any) => {
        if (!trainer) return // Add early return if trainer is null

        // Use Set() to prevent duplicate values, then Array.from() to convert it to an array
        const membersSet = new Set(training?.Members || [])
        membersSet.add(trainer.value)
        const members = Array.from(membersSet)
        setTraining({
            ...training,
            TrainerID: trainer.value,
            Members: members,
        })
        setSelectedMemberList([...selectedMemberList, trainer])
        setSignatureMembers([
            ...signatureMembers,
            {
                MemberID: +trainer.value,
                SignatureData: null,
            },
        ])
    }

    const handleTrainingTypeChange = (trainingTypes: any) => {
        setTraining({
            ...training,
            TrainingTypes: trainingTypes.map((item: any) => item.value),
        })
    }
    /* const handleTrainingLocationChange = (vessel: any) => {
        setTraining({
            ...training,
            VesselID: vessel.isVessel ? vessel.value : 0,
            TrainingLocationID: !vessel.isVessel ? vessel.value : 0,
        })
    } */
    const handleMemberChange = (members: any) => {
        console.log('🔧 TrainingForm - handleMemberChange called with:', {
            members,
            membersLength: members?.length,
            membersType: typeof members,
        })

        const signatures = signatureMembers.filter((item: any) =>
            members.some((m: any) => +m.value === item.MemberID),
        )

        const memberIds = members.map((item: any) => item.value)
        console.log('🔧 TrainingForm - extracted member IDs:', memberIds)

        setTraining({
            ...training,
            Members: memberIds,
            // Signatures: signatures,
        })
        setSelectedMemberList(members)
        setSignatureMembers(signatures)
    }
    const onSignatureChanged = (
        e: string,
        member: string,
        memberId: number,
    ) => {
        const index = signatureMembers.findIndex(
            (object) => object.MemberID === memberId,
        )
        const updatedMembers = [...signatureMembers]
        if (e) {
            if (index !== -1) {
                if (e.trim() === '') {
                    updatedMembers.splice(index, 1)
                } else {
                    updatedMembers[index].SignatureData = e
                }
            } else {
                updatedMembers.push({ MemberID: memberId, SignatureData: e })
            }
        } else {
            updatedMembers.splice(index, 1)
        }
        setSignatureMembers(updatedMembers)
    }

    getTrainingTypeByID(trainingTypeId, setTraining)

    const handleTrainingVesselChange = (vessel: Option | Option[] | null) => {
        setTraining({
            ...training,
            VesselID: vessel
                ? typeof vessel === 'object' && !Array.isArray(vessel)
                    ? vessel.value
                    : 0
                : 0,
        })
    }

    const handleEditorChange = (newContent: any) => {
        setContent(newContent)
    }

    useEffect(() => {
        if (!isEmpty(training)) {
            const vid =
                vesselId > 0 || isNaN(parseInt(training?.VesselID, 10))
                    ? vesselId
                    : parseInt(training?.VesselID, 10)
            setVesselID(vid)
        }
    }, [vesselId, training])

    const [permissions, setPermissions] = useState<any>(false)

    useEffect(() => {
        setPermissions(getPermissions)
    }, [])

    if (
        !permissions ||
        (!hasPermission('EDIT_TRAINING', permissions) &&
            !hasPermission('VIEW_TRAINING', permissions) &&
            !hasPermission('RECORD_TRAINING', permissions))
    ) {
        return !permissions ? (
            <Loading />
        ) : (
            <Loading errorMessage="Oops You do not have the permission to view this section." />
        )
    }

    const getProcedures = () => {
        const procedures = trainingTypes.filter((type: any) =>
            training?.TrainingTypes?.includes(type.id),
        )
        return procedures
            .map((type: any) => {
                return type.customisedComponentField.nodes.length > 0
                    ? {
                          id: type.id,
                          title: type.title,
                          fields: [
                              ...type.customisedComponentField.nodes,
                          ]?.sort(
                              (a: any, b: any) => a.sortOrder - b.sortOrder,
                          ),
                      }
                    : null
            })
            .filter((type: any) => type != null)
    }

    const handleProcedureChecks = (field: any, type: any, status: boolean) => {
        // Always use buffer system for consistency, whether creating or updating
        const procedureCheck = bufferProcedureCheck.filter(
            (procedureField: any) => procedureField.fieldId !== field.id,
        )
        setBufferProcedureCheck([
            ...procedureCheck,
            { fieldId: field.id, status: status },
        ])
    }

    const getFieldStatus = (field: any) => {
        if (bufferProcedureCheck.length > 0) {
            const fieldStatus = bufferProcedureCheck.find(
                (procedureField: any) => procedureField.fieldId == field.id,
            )
            if (fieldStatus) {
                return fieldStatus.status ? 'Ok' : 'Not_Ok'
            }
        }
        const fieldStatus = rawTraining?.procedureFields?.nodes?.find(
            (procedureField: any) =>
                procedureField.customisedComponentFieldID == field.id,
        )
        return fieldStatus?.status || ''
    }

    const showCommentPopup = (field: any) => {
        const fieldComment = rawTraining?.procedureFields?.nodes?.find(
            (procedureField: any) =>
                procedureField.customisedComponentFieldID == field.id,
        )
        if (bufferFieldComment.length > 0) {
            const fieldComment = bufferFieldComment.find(
                (procedureField: any) => procedureField.fieldId == field.id,
            )
            setCurrentComment(fieldComment?.comment || '')
        } else {
            setCurrentComment(fieldComment?.comment || '')
        }
        setCurrentField(field)
        setOpenCommentAlert(true)
    }

    const getComment = (field: any) => {
        if (bufferFieldComment.length > 0) {
            const fieldComment = bufferFieldComment.find(
                (procedureField: any) => procedureField.fieldId == field.id,
            )
            if (fieldComment) {
                return fieldComment.comment
            }
        }
        const fieldComment = rawTraining?.procedureFields?.nodes?.find(
            (procedureField: any) =>
                procedureField.customisedComponentFieldID == field.id,
        )
        return fieldComment?.comment || field.comment
    }

    const handleSaveComment = () => {
        // Always use buffer system for consistency, whether creating or updating
        const fieldComment = bufferFieldComment.filter(
            (procedureField: any) => procedureField.fieldId !== currentField.id,
        )
        setBufferFieldComment([
            ...fieldComment,
            { fieldId: currentField.id, comment: currentComment },
        ])
        setOpenCommentAlert(false)
    }

    return (
        <>
            <Card className="mb-2.5 mx-4">
                <CardHeader>
                    <CardTitle>
                        {trainingID === 0 ? 'New' : 'Edit'} Training Session
                    </CardTitle>
                </CardHeader>
                <Separator className="my-5" />
                {!training && trainingID > 0 ? (
                    <TrainingSessionFormSkeleton />
                ) : (
                    <CardContent className="p-0">
                        <div className="grid lg:grid-cols-3 gap-8">
                            <div className="col-span-3 md:col-span-1 my-4 ">
                                Training Details
                                <p className=" mt-4 max-w-[25rem] leading-loose mb-4"></p>
                                {training &&
                                    trainingTypes.filter(
                                        (type: any) =>
                                            training?.TrainingTypes?.includes(
                                                type.id,
                                            ) && type.procedure,
                                    ).length > 0 && (
                                        <Button
                                            onClick={() =>
                                                setOpenViewProcedure(true)
                                            }>
                                            View Procedures
                                        </Button>
                                    )}
                            </div>
                            <div className="col-span-3 md:col-span-2 pt-8 pb-5 space-y-6 px-7 border border-border border-dashed rounded-lg">
                                <div className="w-full">
                                    <div className="w-full my-4 flex flex-col">
                                        <Label label="Trainer">
                                            <CrewDropdown
                                                value={training?.TrainerID}
                                                vesselID={vesselID}
                                                onChange={handleTrainerChange}
                                            />
                                        </Label>
                                        <small className="text-destructive">
                                            {hasFormErrors &&
                                                formErrors.TrainerID}
                                        </small>
                                    </div>
                                    <div className="w-full md:mt-4 flex flex-col">
                                        <TrainingTypeMultiSelectDropdown
                                            value={training?.TrainingTypes}
                                            onChange={handleTrainingTypeChange}
                                        />
                                        <small className="text-red-vivid-500">
                                            {hasFormErrors &&
                                                formErrors.TrainingTypes}
                                        </small>
                                    </div>
                                </div>
                                <div className="w-full mt-4 flex flex-col">
                                    <Label>Crew</Label>
                                    {(() => {
                                        const crewValue =
                                            memberId > 0
                                                ? [memberId.toString()]
                                                : training?.Members
                                        console.log(
                                            '🔧 TrainingForm - CrewMultiSelectDropdown props:',
                                            {
                                                memberId,
                                                trainingMembers:
                                                    training?.Members,
                                                crewValue,
                                                vesselID,
                                            },
                                        )
                                        return (
                                            <CrewMultiSelectDropdown
                                                value={crewValue}
                                                vesselID={vesselID}
                                                onChange={handleMemberChange}
                                            />
                                        )
                                    })()}
                                </div>
                                <Separator className="my-5" />
                                <div className="flex w-full gap-4 mt-4">
                                    <div className="w-full ">
                                        {/* <DateRangePicker
                                        className="w-full"
                                        onChange={handleTrainingDateChange}
                                    /> */}
                                        <DatePicker
                                            mode="single"
                                            onChange={handleTrainingDateChange}
                                            value={new Date(trainingDate)}
                                        />
                                        <small className="text-destructive">
                                            {hasFormErrors && formErrors.Date}
                                        </small>
                                    </div>
                                    <div className="w-full">
                                        {vessels && (
                                            <Combobox
                                                options={vessels.map(
                                                    (vessel: any) => ({
                                                        label: vessel.label,
                                                        value: vessel.value,
                                                    }),
                                                )}
                                                defaultValues={
                                                    rawTraining?.trainingLocationType ===
                                                    'Vessel'
                                                        ? rawTraining?.VesselID?.toString()
                                                        : rawTraining?.trainingLocationType ===
                                                            'Onshore'
                                                          ? 'Desktop/shore'
                                                          : rawTraining?.trainingLocationType ===
                                                              'Other'
                                                            ? 'Other'
                                                            : rawTraining?.trainingLocationType ===
                                                                    'Location' &&
                                                                rawTraining
                                                                    ?.trainingLocation
                                                                    ?.id > 0
                                                              ? rawTraining?.trainingLocation?.id.toString()
                                                              : vesselId
                                                                ? {
                                                                      label: vessels.find(
                                                                          (
                                                                              vessel: any,
                                                                          ) =>
                                                                              vessel.value ===
                                                                              vesselId,
                                                                      )?.label,
                                                                      value: vesselId.toString(),
                                                                  }
                                                                : null
                                                }
                                                isLoading={rawTraining}
                                                onChange={
                                                    handleTrainingVesselChange
                                                }
                                                placeholder="Select location"
                                            />
                                        )}

                                        <small className="text-destructive">
                                            {hasFormErrors &&
                                                formErrors.VesselID}
                                        </small>
                                    </div>
                                    {/* <div className="w-full">
                                    <TrainingLocationDropdown
                                        value={training?.TrainingLocation}
                                        onChange={handleTrainingLocationChange}
                                    />
                                    <small className="text-destructive">
                                        {hasFormErrors && formErrors.VesselID}
                                    </small>
                                </div> */}
                                </div>
                                <div className="col-span-3 md:col-span-2">
                                    {getProcedures().length > 0 && (
                                        <div className="space-y-8">
                                            {getProcedures().map(
                                                (type: any) => (
                                                    <CheckField key={type.id}>
                                                        <Label
                                                            label={type.title}
                                                        />
                                                        <CheckFieldContent>
                                                            {type.fields.map(
                                                                (
                                                                    field: any,
                                                                ) => (
                                                                    <DailyCheckField
                                                                        key={
                                                                            field.id
                                                                        }
                                                                        displayField={
                                                                            field.status ===
                                                                            'Required'
                                                                        }
                                                                        displayDescription={
                                                                            field.description
                                                                        }
                                                                        displayLabel={
                                                                            field.fieldName
                                                                        }
                                                                        inputId={
                                                                            field.id
                                                                        }
                                                                        handleNoChange={() =>
                                                                            handleProcedureChecks(
                                                                                field,
                                                                                type,
                                                                                false,
                                                                            )
                                                                        }
                                                                        defaultNoChecked={
                                                                            getFieldStatus(
                                                                                field,
                                                                            ) ===
                                                                            'Not_Ok'
                                                                        }
                                                                        handleYesChange={() =>
                                                                            handleProcedureChecks(
                                                                                field,
                                                                                type,
                                                                                true,
                                                                            )
                                                                        }
                                                                        defaultYesChecked={
                                                                            getFieldStatus(
                                                                                field,
                                                                            ) ===
                                                                            'Ok'
                                                                        }
                                                                        commentAction={() =>
                                                                            showCommentPopup(
                                                                                field,
                                                                            )
                                                                        }
                                                                        comment={getComment(
                                                                            field,
                                                                        )}
                                                                        displayImage={
                                                                            trainingID >
                                                                            0
                                                                        }
                                                                        fieldImages={
                                                                            fieldImages
                                                                        }
                                                                        onImageUpload={
                                                                            refreshImages
                                                                        }
                                                                        sectionData={{
                                                                            id: trainingID,
                                                                            sectionName:
                                                                                'trainingSessionID',
                                                                        }}
                                                                    />
                                                                ),
                                                            )}
                                                        </CheckFieldContent>
                                                    </CheckField>
                                                ),
                                            )}
                                        </div>
                                    )}
                                    <div className="my-4 flex items-center w-full">
                                        <Editor
                                            id="TrainingSummary"
                                            placeholder="Summary of training, identify any outcomes, further training required or other observations."
                                            className="!w-full  ring-1 ring-inset "
                                            handleEditorChange={
                                                handleEditorChange
                                            }
                                            content={content}
                                        />
                                    </div>
                                </div>
                                {/*<div className="w-full my-4 flex flex-col">
                                <textarea
                                    id="TrainingSummary"
                                    placeholder="Summary"
                                    defaultValue={training?.TrainingSummary}
                                    rows={4}
                                    className={''}></textarea>
                            </div>*/}
                            </div>
                        </div>
                        <Separator className="my-5" />
                        <div className="grid lg:grid-cols-3 gap-6">
                            <div className="col-span-3 md:col-span-1 md:my-4 ">
                                Signatures
                            </div>
                            <div className="col-span-3 sm:col-span-2 md:my-4 flex justify-between flex-wrap gap-4">
                                {selectedMemberList &&
                                    selectedMemberList.map(
                                        (member: any, index: number) => (
                                            <div
                                                className="w-full md:w-96"
                                                key={index}>
                                                <SignaturePad
                                                    className="w-full"
                                                    member={member.label}
                                                    memberId={member.value}
                                                    onSignatureChanged={(
                                                        signature: string,
                                                        member?: string,
                                                        memberId?: number,
                                                    ) =>
                                                        onSignatureChanged(
                                                            signature,
                                                            member ?? '',
                                                            memberId || 0,
                                                        )
                                                    }
                                                    signature={{
                                                        id: signatureMembers.find(
                                                            (sig: any) =>
                                                                sig.MemberID ===
                                                                member.value,
                                                        )?.ID,
                                                    }}
                                                />
                                            </div>
                                        ),
                                    )}
                            </div>
                        </div>
                    </CardContent>
                )}
            </Card>
            <FooterWrapper>
                <Button
                    variant="back"
                    onClick={() => router.push('/crew-training')}
                    iconLeft={ArrowLeft}>
                    Cancel
                </Button>
                {/* <Button className="group inline-flex justify-center items-center mr-8 rounded-md bg-rose-100 px-3 py-2  text-rose-600 shadow-sm hover: hover:text-rose-600 ring-1 ring-rose-600">
                                <svg fill="none" viewBox="0 0 24 24" strokeWidth="1.5" stroke="currentColor" className="-ml-0.5 mr-1.5 h-5 w-5 border border-rose-600 group-hover:border-white rounded-full group-hover:bg-rose-600 group-hover:">
                                    <path strokeLinecap="round" strokeLinejoin="round" d="M6 18 18 6M6 6l12 12" />
                                </svg>
                                Delete task
                            </Button> */}
                <Button
                    onClick={handleSave}
                    disabled={
                        mutationCreateTrainingSessionLoading ||
                        mutationUpdateTrainingSessionLoading
                    }>
                    {trainingID === 0 ? 'Create session' : 'Update session'}
                </Button>
            </FooterWrapper>
            <Sheet open={openViewProcedure} onOpenChange={setOpenViewProcedure}>
                <SheetContent side="right" className="w-[400px] sm:w-[540px]">
                    <SheetHeader>
                        <SheetTitle>Procedures</SheetTitle>
                    </SheetHeader>

                    {training &&
                        trainingTypes
                            .filter(
                                (type: any) =>
                                    training?.TrainingTypes?.includes(
                                        type.id,
                                    ) && type.procedure,
                            )
                            .map((type: any) => (
                                <div
                                    key={type.id}
                                    className="mb-4 px-2.5 sm:px-5">
                                    <H4>{type.title}</H4>

                                    <div
                                        dangerouslySetInnerHTML={{
                                            __html: type.procedure,
                                        }}
                                    />
                                </div>
                            ))}
                </SheetContent>
            </Sheet>
            <AlertDialog
                open={openCommentAlert}
                onOpenChange={setOpenCommentAlert}>
                <AlertDialogContent>
                    <AlertDialogHeader>
                        <AlertDialogTitle>Add Comment</AlertDialogTitle>
                        <AlertDialogDescription>
                            Add a comment for this procedure check.
                        </AlertDialogDescription>
                    </AlertDialogHeader>
                    <Textarea
                        value={currentComment}
                        onChange={(e) => setCurrentComment(e.target.value)}
                        placeholder="Enter your comment here..."
                        rows={4}
                    />
                    <AlertDialogFooter>
                        <AlertDialogCancel>Cancel</AlertDialogCancel>
                        <AlertDialogAction onClick={handleSaveComment}>
                            Save Comment
                        </AlertDialogAction>
                    </AlertDialogFooter>
                </AlertDialogContent>
            </AlertDialog>
        </>
    )
}

export default TrainingForm
