'use client'
import { useEffect, useState } from 'react'
import { isEmpty } from 'lodash'
import { CREATE_USER } from '@/app/lib/graphQL/mutation'
import { useLazyQuery, useMutation } from '@apollo/client'

import SeaLogsMemberModel from '@/app/offline/models/seaLogsMember'
import { generateUniqueId } from '@/app/offline/helpers/functions'
import { Combobox, Option } from '@/components/ui/comboBox'
import { AddCrewMemberDialog } from '../add-crew-member-dialog'
import { ReadSeaLogsMembers } from './queries'

const CrewMultiSelectDropdown = ({
    value = [], // an array of crew IDs
    onChange,
    memberIdOptions = [],
    departments = [],
    filterByAdmin = false,
    offline = false,
    vesselID = 0,
}: {
    value: any[]
    onChange: any
    memberIdOptions?: any[]
    departments?: any
    filterByAdmin?: boolean
    offline?: boolean
    vesselID?: number
}) => {
    const [isLoading, setIsLoading] = useState(true)
    const [crewList, setCrewList] = useState([] as any)
    const [openCreateMemberDialog, setOpenCreateMemberDialog] = useState(false)
    const [selectedIDs, setSelectedIDs] = useState([] as any)
    const [error, setError] = useState<any>(false)
    const seaLogsMemberModel = new SeaLogsMemberModel()
    const handleSetCrewList = (crewListRaw: any) => {
        console.log(
            '🔧 CrewMultiSelectDropdown - handleSetCrewList called with:',
            {
                crewListRawLength: crewListRaw?.length,
                vesselID,
                departments: departments.length,
                memberIdOptions: memberIdOptions.length,
            },
        )

        // If vesselID > 0, filter the crew list to display only the vessel crew.
        const vesselCrewList =
            vesselID > 0
                ? crewListRaw.filter((crew: any) =>
                      crew.vehicles.nodes.some(
                          (vehicle: any) => +vehicle.id === vesselID,
                      ),
                  )
                : crewListRaw

        console.log('🔧 CrewMultiSelectDropdown - vesselCrewList filtered:', {
            originalLength: crewListRaw?.length,
            filteredLength: vesselCrewList?.length,
        })

        const createOption = {
            value: 'newCrewMember',
            label: '--- Create Crew Member ---',
        }
        const data = vesselCrewList.filter((crew: any) =>
            filterByAdmin ? !crewIsAdmin(crew) : true,
        )

        if (departments.length > 0) {
            const departmentList = departments.flatMap((department: any) => {
                return department.id
            })
            const crews = data
                .filter((crew: any) =>
                    crew.departments.nodes.some((node: any) =>
                        departmentList.includes(node.id),
                    ),
                )
                .map((item: any) => {
                    return {
                        value: item.id,
                        label: `${item.firstName ?? ''} ${item.surname ?? ''}`,
                        profile: {
                            firstName: item.firstName,
                            surname: item.surname,
                            avatar: null,
                        },
                    }
                })
            if (memberIdOptions.length === 0) {
                setCrewList([createOption, ...crews])
            } else {
                const filteredCrewList = crews.filter((crew: any) => {
                    return memberIdOptions.includes(crew.value)
                })
                setCrewList(filteredCrewList)
            }
        } else {
            const crews = data.map((item: any) => {
                return {
                    value: item.id,
                    label: `${item.firstName ?? ''} ${item.surname ?? ''}`,
                    profile: {
                        firstName: item.firstName,
                        surname: item.surname,
                        avatar: null,
                    },
                }
            })
            if (memberIdOptions.length === 0) {
                setCrewList([createOption, ...crews])
            } else {
                const filteredCrewList = crews.filter((crew: any) => {
                    return memberIdOptions.includes(crew.value)
                })
                setCrewList(filteredCrewList)
            }
        }

        // Log will be handled after setCrewList is called
    }
    const [querySeaLogsMembersList] = useLazyQuery(ReadSeaLogsMembers, {
        fetchPolicy: 'cache-and-network',
        onError: (error: any) => {
            console.error('querySeaLogsMembersList error', error)
        },
    })
    const loadCrewMembers = async () => {
        console.log('🔧 CrewMultiSelectDropdown - loadCrewMembers started')
        let allMembers: any[] = []
        let offset = 0
        const limit = 100
        let hasNextPage = true

        try {
            while (hasNextPage) {
                console.log(
                    `🔧 CrewMultiSelectDropdown - fetching page: offset=${offset}, limit=${limit}`,
                )
                const response = await querySeaLogsMembersList({
                    variables: {
                        filter: { isArchived: { eq: false } },
                        limit: limit,
                        offset: offset,
                    },
                })

                if (response.data?.readSeaLogsMembers) {
                    const data = response.data.readSeaLogsMembers.nodes
                    const pageInfo = response.data.readSeaLogsMembers.pageInfo

                    console.log(`🔧 CrewMultiSelectDropdown - page data:`, {
                        dataLength: data?.length,
                        hasNextPage: pageInfo?.hasNextPage,
                    })

                    if (data && data.length > 0) {
                        allMembers = [...allMembers, ...data]
                    }

                    hasNextPage = pageInfo?.hasNextPage || false
                    offset += limit
                } else {
                    hasNextPage = false
                }
            }

            console.log(
                '🔧 CrewMultiSelectDropdown - loadCrewMembers completed:',
                {
                    totalMembers: allMembers.length,
                },
            )

            // Set all collected members at once
            if (allMembers.length > 0) {
                handleSetCrewList(allMembers)
            }
        } catch (error) {
            console.error(
                '🔧 CrewMultiSelectDropdown - Error loading all crew members:',
                error,
            )
        }
    }
    useEffect(() => {
        if (isLoading && !offline) {
            loadCrewMembers()
            setIsLoading(false)
        }
    }, [isLoading, offline])
    // if (!offline) {
    // getSeaLogsMembersList(handleSetCrewList)
    // }

    useEffect(() => {
        if (offline) {
            seaLogsMemberModel.getAll().then((data: any) => {
                handleSetCrewList(data)
            })
        }
    }, [offline])

    // Log when crewList changes
    useEffect(() => {
        console.log('🔧 CrewMultiSelectDropdown - crewList updated:', {
            crewListLength: crewList?.length,
            sampleCrew: crewList?.[0],
            hasProfiles: crewList?.[0]?.profile ? 'Yes' : 'No',
        })
    }, [crewList])

    const crewIsAdmin = (crew: any) => {
        return (
            crew.groups.nodes?.filter((permission: any) => {
                return permission.code === 'admin'
            }).length > 0
        )
    }

    const handleOnChange = (value: Option | Option[] | null) => {
        console.log(
            '🔧 CrewMultiSelectDropdown - handleOnChange called with:',
            {
                value,
                valueType: typeof value,
                isArray: Array.isArray(value),
            },
        )

        if (!value) {
            console.log(
                '🔧 CrewMultiSelectDropdown - clearing selection (no value)',
            )
            setSelectedIDs([])
            onChange([])
            return
        }

        const valueArray = Array.isArray(value) ? value : [value]
        console.log('🔧 CrewMultiSelectDropdown - valueArray:', valueArray)

        // Fix the condition to properly check for 'newCrewMember'
        if (
            valueArray.find((option: any) => option.value === 'newCrewMember')
        ) {
            console.log(
                '🔧 CrewMultiSelectDropdown - opening create member dialog',
            )
            setOpenCreateMemberDialog(true)
            return
        }

        if (valueArray.length === 0) {
            console.log(
                '🔧 CrewMultiSelectDropdown - clearing selection (empty array)',
            )
            setSelectedIDs([])
            onChange([])
            return
        }

        // Ensure we're working with valid Option objects that have both value and label
        const validOptions = valueArray.filter(
            (option) =>
                option &&
                typeof option === 'object' &&
                option.value !== undefined &&
                option.label !== null &&
                option.label !== undefined,
        )

        console.log('🔧 CrewMultiSelectDropdown - validOptions filtered:', {
            originalLength: valueArray.length,
            validLength: validOptions.length,
            validOptions,
        })

        setSelectedIDs(validOptions)
        onChange(validOptions)
    }

    const [queryAddMember] = useMutation(CREATE_USER, {
        fetchPolicy: 'no-cache',
        onCompleted: (response: any) => {
            const data = response.createSeaLogsMember
            if (data.id > 0) {
                setOpenCreateMemberDialog(false)
                const newData = {
                    value: data.id,
                    label: data.firstName + ' ' + data.surname,
                }
                setCrewList([...crewList, newData])
                const newSelectedIDs = [...selectedIDs, newData]
                setSelectedIDs(newSelectedIDs)
                onChange(newSelectedIDs)
                setError(false)
            }
        },
        onError: (error: any) => {
            console.error('createUser error', error.message)
            setError(error)
        },
    })

    const handleAddNewMember = async () => {
        const variables = {
            input: {
                firstName: (
                    document.getElementById(
                        'crew-firstName',
                    ) as HTMLInputElement
                ).value
                    ? (
                          document.getElementById(
                              'crew-firstName',
                          ) as HTMLInputElement
                      ).value
                    : null,
                surname: (
                    document.getElementById('crew-surname') as HTMLInputElement
                ).value
                    ? (
                          document.getElementById(
                              'crew-surname',
                          ) as HTMLInputElement
                      ).value
                    : null,
                email: (
                    document.getElementById('crew-email') as HTMLInputElement
                ).value
                    ? (
                          document.getElementById(
                              'crew-email',
                          ) as HTMLInputElement
                      ).value
                    : null,
                phoneNumber: (
                    document.getElementById(
                        'crew-phoneNumber',
                    ) as HTMLInputElement
                ).value
                    ? (
                          document.getElementById(
                              'crew-phoneNumber',
                          ) as HTMLInputElement
                      ).value
                    : null,
                username: (
                    document.getElementById('crew-username') as HTMLInputElement
                ).value
                    ? (
                          document.getElementById(
                              'crew-username',
                          ) as HTMLInputElement
                      ).value
                    : null,
                password: (
                    document.getElementById('crew-password') as HTMLInputElement
                ).value
                    ? (
                          document.getElementById(
                              'crew-password',
                          ) as HTMLInputElement
                      ).value
                    : null,
            },
        }
        if (offline) {
            // queryAddMember
            const data = await seaLogsMemberModel.save({
                ...variables.input,
                id: generateUniqueId(),
            })
            setOpenCreateMemberDialog(false)
            const newData = {
                value: data.id,
                label: data.firstName + ' ' + data.surname,
            }
            setCrewList([...crewList, newData])
            const newSelectedIDs = [...selectedIDs, newData]
            setSelectedIDs(newSelectedIDs)
            onChange(newSelectedIDs)
            setError(false)
        } else {
            await queryAddMember({
                variables: variables,
            })
        }
    }

    useEffect(() => {
        console.log(
            '🔧 CrewMultiSelectDropdown - useEffect [value, crewList] triggered:',
            {
                valueLength: value?.length,
                value,
                crewListLength: crewList?.length,
                crewListEmpty: isEmpty(crewList),
                valueEmpty: isEmpty(value),
            },
        )

        if (!isEmpty(crewList) && !isEmpty(value)) {
            console.log(
                '🔧 CrewMultiSelectDropdown - mapping values to options',
            )
            // Convert value array to proper Option objects
            const selectedOptions = value
                .map((id) => {
                    const option = crewList.find(
                        (crew: any) => crew.value === id,
                    )
                    console.log(
                        `🔧 CrewMultiSelectDropdown - mapping ID ${id}:`,
                        {
                            found: !!option,
                            option,
                        },
                    )
                    return option
                })
                .filter(Boolean) // Remove any undefined/null options

            console.log(
                '🔧 CrewMultiSelectDropdown - selectedOptions after mapping:',
                {
                    originalValueLength: value.length,
                    selectedOptionsLength: selectedOptions.length,
                    selectedOptions,
                },
            )

            setSelectedIDs(selectedOptions)
        } else if (isEmpty(value)) {
            console.log(
                '🔧 CrewMultiSelectDropdown - clearing selection (empty value)',
            )
            // Clear selection when value is empty
            setSelectedIDs([])
        }
    }, [value, crewList])

    return (
        <>
            <Combobox
                options={crewList}
                value={selectedIDs} // Use value instead of defaultValues for controlled component
                onChange={handleOnChange}
                placeholder="Select Crew"
                multi
                isLoading={isLoading || crewList.length === 0}
            />
            <AddCrewMemberDialog
                openDialog={openCreateMemberDialog}
                setOpenDialog={setOpenCreateMemberDialog}
                handleCreate={handleAddNewMember}
                actionText="Add Crew Member"
                error={error}
            />
        </>
    )
}

export default CrewMultiSelectDropdown
