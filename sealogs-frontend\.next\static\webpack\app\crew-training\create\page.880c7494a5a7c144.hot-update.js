"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/crew-training/create/page",{

/***/ "(app-pages-browser)/./src/app/ui/crew-training/create/form.tsx":
/*!**************************************************!*\
  !*** ./src/app/ui/crew-training/create/form.tsx ***!
  \**************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! dayjs */ \"(app-pages-browser)/./node_modules/.pnpm/dayjs@1.11.13/node_modules/dayjs/dayjs.min.js\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(dayjs__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var lodash_isEmpty__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! lodash/isEmpty */ \"(app-pages-browser)/./node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/isEmpty.js\");\n/* harmony import */ var lodash_isEmpty__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(lodash_isEmpty__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _components_filter_components_crew_dropdown_crew_dropdown__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../../../components/filter/components/crew-dropdown/crew-dropdown */ \"(app-pages-browser)/./src/components/filter/components/crew-dropdown/crew-dropdown.tsx\");\n/* harmony import */ var _components_skeletons__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../../../components/skeletons */ \"(app-pages-browser)/./src/components/skeletons.tsx\");\n/* harmony import */ var _type_multiselect_dropdown__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../type-multiselect-dropdown */ \"(app-pages-browser)/./src/app/ui/crew-training/type-multiselect-dropdown.tsx\");\n/* harmony import */ var _crew_multiselect_dropdown_multiselect_dropdown__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../../crew/multiselect-dropdown/multiselect-dropdown */ \"(app-pages-browser)/./src/app/ui/crew/multiselect-dropdown/multiselect-dropdown.tsx\");\n/* harmony import */ var _app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/app/lib/graphQL/mutation */ \"(app-pages-browser)/./src/app/lib/graphQL/mutation/index.ts\");\n/* harmony import */ var _app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/app/lib/graphQL/query */ \"(app-pages-browser)/./src/app/lib/graphQL/query/index.ts\");\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! @apollo/client */ \"(app-pages-browser)/./node_modules/.pnpm/@apollo+client@3.13.8_@type_3a2c516023e671829487810ec4a9ea8f/node_modules/@apollo/client/react/hooks/useLazyQuery.js\");\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_30__ = __webpack_require__(/*! @apollo/client */ \"(app-pages-browser)/./node_modules/.pnpm/@apollo+client@3.13.8_@type_3a2c516023e671829487810ec4a9ea8f/node_modules/@apollo/client/react/hooks/useMutation.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _editor__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ../../editor */ \"(app-pages-browser)/./src/app/ui/editor.tsx\");\n/* harmony import */ var _app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/app/helpers/userHelper */ \"(app-pages-browser)/./src/app/helpers/userHelper.ts\");\n/* harmony import */ var _app_loading__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/app/loading */ \"(app-pages-browser)/./src/app/loading.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./src/components/ui/label.tsx\");\n/* harmony import */ var _components_ui_comboBox__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/components/ui/comboBox */ \"(app-pages-browser)/./src/components/ui/comboBox.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_separator__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @/components/ui/separator */ \"(app-pages-browser)/./src/components/ui/separator.tsx\");\n/* harmony import */ var _components_ui_sheet__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @/components/ui/sheet */ \"(app-pages-browser)/./src/components/ui/sheet.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_signature_pad__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! @/components/signature-pad */ \"(app-pages-browser)/./src/components/signature-pad.tsx\");\n/* harmony import */ var _components_footer_wrapper__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! @/components/footer-wrapper */ \"(app-pages-browser)/./src/components/footer-wrapper.tsx\");\n/* harmony import */ var _components_daily_check_field__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! @/components/daily-check-field */ \"(app-pages-browser)/./src/components/daily-check-field.tsx\");\n/* harmony import */ var _components_DateRange__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! @/components/DateRange */ \"(app-pages-browser)/./src/components/DateRange.tsx\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! @/hooks/use-toast */ \"(app-pages-browser)/./src/hooks/use-toast.ts\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_lucide_react__WEBPACK_IMPORTED_MODULE_31__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.474.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _components_ui__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! @/components/ui */ \"(app-pages-browser)/./src/components/ui/index.ts\");\n/* harmony import */ var _components_ui_alert_dialog__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! @/components/ui/alert-dialog */ \"(app-pages-browser)/./src/components/ui/alert-dialog.tsx\");\n/* harmony import */ var _components_ui_textarea__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! @/components/ui/textarea */ \"(app-pages-browser)/./src/components/ui/textarea.tsx\");\n/* harmony import */ var _queries__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! ./queries */ \"(app-pages-browser)/./src/app/ui/crew-training/create/queries.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst TrainingForm = (param)=>{\n    let { trainingID = 0, memberId = 0, trainingTypeId = 0, vesselId = 0 } = param;\n    var _rawTraining_VesselID, _rawTraining_trainingLocation, _rawTraining_trainingLocation1, _vessels_find;\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_10__.useRouter)();\n    const { toast } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_24__.useToast)();\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(true);\n    const [training, setTraining] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)({});\n    const [rawTraining, setRawTraining] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)();\n    const [trainingDate, setTrainingDate] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(new Date());\n    const [hasFormErrors, setHasFormErrors] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    const [selectedMemberList, setSelectedMemberList] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)([]);\n    const [signatureMembers, setSignatureMembers] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)([]);\n    const [vessels, setVessels] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)();\n    const [trainingTypes, setTrainingTypes] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)([]);\n    const [content, setContent] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(\"\");\n    const [openViewProcedure, setOpenViewProcedure] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    const [currentComment, setCurrentComment] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(\"\");\n    const [currentField, setCurrentField] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(\"\");\n    const [bufferProcedureCheck, setBufferProcedureCheck] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)([]);\n    const [bufferFieldComment, setBufferFieldComment] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)([]);\n    const [openCommentAlert, setOpenCommentAlert] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    const [formErrors, setFormErrors] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)({\n        TrainingTypes: \"\",\n        TrainerID: \"\",\n        VesselID: \"\",\n        Date: \"\"\n    });\n    const [vesselID, setVesselID] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(vesselId);\n    const [fieldImages, setFieldImages] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    // GraphQL queries to replace the problematic function calls\n    const [queryTrainingTypes] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_29__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_9__.CREW_TRAINING_TYPES, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            const data = response.readTrainingTypes.nodes;\n            if (data) {\n                setTrainingTypes(data);\n            }\n        },\n        onError: (error)=>{\n            console.error(\"queryTrainingTypes error\", error);\n        }\n    });\n    const [queryTrainingSessionByID] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_29__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_9__.TRAINING_SESSION_BY_ID, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            const data = response.readOneTrainingSession;\n            if (data) {\n                handleSetTraining(data);\n            }\n        },\n        onError: (error)=>{\n            console.error(\"queryTrainingSession error\", error);\n        }\n    });\n    const [queryTrainingTypeByID] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_29__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_9__.TRAINING_TYPE_BY_ID, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            const data = response.readOneTrainingType;\n            if (data) {\n                setTraining((prevTraining)=>({\n                        ...prevTraining,\n                        TrainingTypes: [\n                            data.id\n                        ]\n                    }));\n            }\n        },\n        onError: (error)=>{\n            console.error(\"queryTrainingType error\", error);\n        }\n    });\n    const [getFieldImages] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_29__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_9__.GET_SECTION_MEMBER_IMAGES, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            const data = response.readCaptureImages.nodes;\n            if (data) {\n                setFieldImages(data);\n            }\n        },\n        onError: (error)=>{\n            console.error(\"getFieldImages error\", error);\n        }\n    });\n    // useEffect hooks to replace the problematic function calls\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)(()=>{\n        // Load training types on component mount\n        queryTrainingTypes();\n    }, []) // Empty dependency array - only run once on mount\n    ;\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)(()=>{\n        // Load training session by ID when trainingID changes\n        if (trainingID > 0) {\n            queryTrainingSessionByID({\n                variables: {\n                    id: trainingID\n                }\n            });\n        }\n    }, [\n        trainingID\n    ]) // Only depend on trainingID\n    ;\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)(()=>{\n        // Load training type by ID when trainingTypeId changes\n        if (trainingTypeId > 0) {\n            queryTrainingTypeByID({\n                variables: {\n                    id: trainingTypeId\n                }\n            });\n        }\n    }, [\n        trainingTypeId\n    ]) // Only depend on trainingTypeId\n    ;\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)(()=>{\n        if (trainingID > 0) {\n            getFieldImages({\n                variables: {\n                    filter: {\n                        trainingSessionID: {\n                            eq: trainingID\n                        }\n                    }\n                }\n            });\n        }\n    }, [\n        trainingID\n    ]) // Only depend on trainingID\n    ;\n    const refreshImages = async ()=>{\n        await getFieldImages({\n            variables: {\n                filter: {\n                    trainingSessionID: {\n                        eq: trainingID\n                    }\n                }\n            }\n        });\n    };\n    const handleSetTraining = (training)=>{\n        var _training_procedureFields;\n        const tDate = new Date(training.date);\n        setTrainingDate(tDate);\n        const trainingData = {\n            ID: trainingID,\n            Date: dayjs__WEBPACK_IMPORTED_MODULE_1___default()(training.date).format(\"YYYY-MM-DD\"),\n            Members: training.members.nodes.map((m)=>m.id),\n            TrainerID: training.trainer.id,\n            TrainingSummary: training.trainingSummary,\n            TrainingTypes: training.trainingTypes.nodes.map((t)=>t.id),\n            // VesselID: training.vessel.id,\n            VesselID: training.vesselID\n        };\n        setRawTraining(training);\n        setTraining(trainingData);\n        setContent(training.trainingSummary);\n        const members = training.members.nodes.map((m)=>{\n            var _m_firstName, _m_surname;\n            return {\n                label: \"\".concat((_m_firstName = m.firstName) !== null && _m_firstName !== void 0 ? _m_firstName : \"\", \" \").concat((_m_surname = m.surname) !== null && _m_surname !== void 0 ? _m_surname : \"\"),\n                value: m.id\n            };\n        }) || [];\n        const vesselCrewIds = training.vessel.seaLogsMembers.nodes.map((slm)=>+slm.id);\n        const vesselCrews = members.filter((m)=>vesselCrewIds.includes(+m.value));\n        setSelectedMemberList(vesselCrews);\n        const signatures = training.signatures.nodes.map((s)=>({\n                MemberID: s.member.id,\n                SignatureData: s.signatureData,\n                ID: s.id\n            }));\n        setSignatureMembers(signatures);\n        // Initialize buffer with existing procedure field data for updates\n        if ((_training_procedureFields = training.procedureFields) === null || _training_procedureFields === void 0 ? void 0 : _training_procedureFields.nodes) {\n            const existingProcedureChecks = training.procedureFields.nodes.map((field)=>({\n                    fieldId: field.customisedComponentFieldID,\n                    status: field.status === \"Ok\"\n                }));\n            setBufferProcedureCheck(existingProcedureChecks);\n            const existingFieldComments = training.procedureFields.nodes.filter((field)=>field.comment).map((field)=>({\n                    fieldId: field.customisedComponentFieldID,\n                    comment: field.comment\n                }));\n            setBufferFieldComment(existingFieldComments);\n        }\n    };\n    const handleSetVessels = (data)=>{\n        const activeVessels = data === null || data === void 0 ? void 0 : data.filter((vessel)=>!vessel.archived);\n        const formattedData = [\n            {\n                label: \"Other\",\n                value: \"Other\"\n            },\n            {\n                label: \"Desktop/shore\",\n                value: \"Onshore\"\n            },\n            ...activeVessels.map((vessel)=>({\n                    value: vessel.id,\n                    label: vessel.title\n                }))\n        ];\n        setVessels(formattedData);\n    };\n    const [queryVessels] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_29__.useLazyQuery)(_queries__WEBPACK_IMPORTED_MODULE_28__.ReadVessels, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (queryVesselResponse)=>{\n            if (queryVesselResponse.readVessels.nodes) {\n                handleSetVessels(queryVesselResponse.readVessels.nodes);\n            }\n        },\n        onError: (error)=>{\n            console.error(\"queryVessels error\", error);\n        }\n    });\n    const loadVessels = async ()=>{\n        await queryVessels({\n            variables: {\n                limit: 200,\n                offset: 0\n            }\n        });\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)(()=>{\n        if (isLoading) {\n            loadVessels();\n            setIsLoading(false);\n        }\n    }, [\n        isLoading\n    ]);\n    const [mutationCreateTrainingSession, { loading: mutationCreateTrainingSessionLoading }] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_30__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_8__.CREATE_TRAINING_SESSION, {\n        onCompleted: (response)=>{\n            const data = response.createTrainingSession;\n            if (data.id > 0) {\n                if (bufferProcedureCheck.length > 0) {\n                    const procedureFields = bufferProcedureCheck.map((procedureField)=>{\n                        var _bufferFieldComment_find;\n                        return {\n                            status: procedureField.status ? \"Ok\" : \"Not_Ok\",\n                            trainingSessionID: data.id,\n                            customisedComponentFieldID: procedureField.fieldId,\n                            comment: (_bufferFieldComment_find = bufferFieldComment.find((comment)=>comment.fieldId == procedureField.fieldId)) === null || _bufferFieldComment_find === void 0 ? void 0 : _bufferFieldComment_find.comment\n                        };\n                    });\n                    procedureFields.forEach((procedureField)=>{\n                        createCustomisedComponentFieldData({\n                            variables: {\n                                input: procedureField\n                            }\n                        });\n                    });\n                }\n                updateTrainingSessionDues();\n                updateSignatures(data.id);\n                handleEditorChange(data.trainingSummary);\n                router.push(\"/crew-training\");\n            } else {\n                console.error(\"mutationCreateUser error\", response);\n            }\n        },\n        onError: (error)=>{\n            console.error(\"mutationCreateTrainingSession error\", error);\n        }\n    });\n    const [mutationUpdateTrainingSession, { loading: mutationUpdateTrainingSessionLoading }] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_30__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_8__.UPDATE_TRAINING_SESSION, {\n        onCompleted: (response)=>{\n            const data = response.updateTrainingSession;\n            if (data.id > 0) {\n                // Handle procedure checks for updates\n                if (bufferProcedureCheck.length > 0) {\n                    const procedureFields = bufferProcedureCheck.map((procedureField)=>{\n                        var _rawTraining_procedureFields_nodes, _rawTraining_procedureFields, _bufferFieldComment_find;\n                        const existingField = rawTraining === null || rawTraining === void 0 ? void 0 : (_rawTraining_procedureFields = rawTraining.procedureFields) === null || _rawTraining_procedureFields === void 0 ? void 0 : (_rawTraining_procedureFields_nodes = _rawTraining_procedureFields.nodes) === null || _rawTraining_procedureFields_nodes === void 0 ? void 0 : _rawTraining_procedureFields_nodes.find((field)=>field.customisedComponentFieldID === procedureField.fieldId);\n                        return {\n                            id: existingField === null || existingField === void 0 ? void 0 : existingField.id,\n                            status: procedureField.status ? \"Ok\" : \"Not_Ok\",\n                            trainingSessionID: data.id,\n                            customisedComponentFieldID: procedureField.fieldId,\n                            comment: (_bufferFieldComment_find = bufferFieldComment.find((comment)=>comment.fieldId == procedureField.fieldId)) === null || _bufferFieldComment_find === void 0 ? void 0 : _bufferFieldComment_find.comment\n                        };\n                    });\n                    procedureFields.forEach((procedureField)=>{\n                        if (procedureField.id) {\n                            // Update existing field\n                            updateCustomisedComponentFieldData({\n                                variables: {\n                                    input: procedureField\n                                }\n                            });\n                        } else {\n                            // Create new field\n                            const { id, ...createInput } = procedureField;\n                            createCustomisedComponentFieldData({\n                                variables: {\n                                    input: createInput\n                                }\n                            });\n                        }\n                    });\n                }\n                updateTrainingSessionDues();\n                updateSignatures(trainingID);\n                handleEditorChange(data.trainingSummary);\n                if (+memberId > 0) {\n                    router.push(\"/crew/info?id=\".concat(memberId));\n                } else if (+vesselId > 0) {\n                    router.push(\"/vessel/info?id=\".concat(vesselId));\n                } else {\n                    router.push(\"/crew-training\");\n                }\n            } else {\n                console.error(\"mutationUpdateTrainingSession error\", response);\n            }\n        },\n        onError: (error)=>{\n            console.error(\"mutationUpdateTrainingSession error\", error);\n        }\n    });\n    const [readOneTrainingSessionDue, { loading: readOneTrainingSessionDueLoading }] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_29__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_9__.READ_ONE_TRAINING_SESSION_DUE, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            return response.readOneTrainingSessionDue.data;\n        },\n        onError: (error)=>{\n            console.error(\"readOneTrainingSessionDueLoading error:\", error);\n            return null;\n        }\n    });\n    const getTrainingSessionDueWithVariables = async function() {\n        let variables = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {}, onCompleted = arguments.length > 1 ? arguments[1] : void 0;\n        const { data } = await readOneTrainingSessionDue({\n            variables: variables\n        });\n        onCompleted(data.readOneTrainingSessionDue);\n    };\n    const [mutationCreateTrainingSessionDue, { loading: createTrainingSessionDueLoading }] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_30__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_8__.CREATE_TRAINING_SESSION_DUE, {\n        onCompleted: (response)=>{},\n        onError: (error)=>{\n            console.error(\"createTrainingSessionDue error\", error);\n        }\n    });\n    const [mutationUpdateTrainingSessionDue, { loading: updateTrainingSessionDueLoading }] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_30__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_8__.UPDATE_TRAINING_SESSION_DUE, {\n        onCompleted: (response)=>{},\n        onError: (error)=>{\n            console.error(\"updateTrainingSessionDue error\", error);\n        }\n    });\n    const updateTrainingSessionDues = async ()=>{\n        const trainingSessionDues = [];\n        const vesselID = training.VesselID;\n        training.TrainingTypes.forEach((t)=>{\n            const trainingInfo = trainingTypes.find((tt)=>tt.id === t);\n            if (!lodash_isEmpty__WEBPACK_IMPORTED_MODULE_2___default()(trainingInfo) && trainingInfo.occursEvery > 0) {\n                const trainingTypeID = t;\n                const newDueDate = dayjs__WEBPACK_IMPORTED_MODULE_1___default()(training.Date).add(trainingInfo.occursEvery, \"day\");\n                training.Members.forEach((m)=>{\n                    const memberID = m;\n                    trainingSessionDues.push({\n                        dueDate: newDueDate.format(\"YYYY-MM-DD\"),\n                        memberID: memberID,\n                        vesselID: vesselID,\n                        trainingTypeID: trainingTypeID\n                    });\n                });\n            }\n        });\n        let trainingSessionDueWithIDs = [];\n        if (!lodash_isEmpty__WEBPACK_IMPORTED_MODULE_2___default()(trainingSessionDues)) {\n            await Promise.all(trainingSessionDues.map(async (item)=>{\n                const variables = {\n                    filter: {\n                        memberID: {\n                            eq: item.memberID\n                        },\n                        vesselID: {\n                            eq: item.vesselID\n                        },\n                        trainingTypeID: {\n                            eq: item.trainingTypeID\n                        }\n                    }\n                };\n                const onCompleted = (response)=>{\n                    var _response_id;\n                    trainingSessionDueWithIDs.push({\n                        ...item,\n                        id: (_response_id = response === null || response === void 0 ? void 0 : response.id) !== null && _response_id !== void 0 ? _response_id : 0\n                    });\n                };\n                await getTrainingSessionDueWithVariables(variables, onCompleted);\n            }));\n        }\n        if (!lodash_isEmpty__WEBPACK_IMPORTED_MODULE_2___default()(trainingSessionDueWithIDs)) {\n            await Promise.all(Array.from(trainingSessionDueWithIDs).map(async (item)=>{\n                const variables = {\n                    variables: {\n                        input: item\n                    }\n                };\n                if (item.id === 0) {\n                    await mutationCreateTrainingSessionDue(variables);\n                } else {\n                    await mutationUpdateTrainingSessionDue(variables);\n                }\n            }));\n        }\n    };\n    const [createCustomisedComponentFieldData] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_30__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_8__.CREATE_CUSTOMISED_COMPONENT_FIELD_DATA, {\n        onCompleted: (response)=>{\n            var _rawTraining_procedureFields;\n            const data = response.createCustomisedComponentFieldData;\n            if (data.id > 0 && (rawTraining === null || rawTraining === void 0 ? void 0 : (_rawTraining_procedureFields = rawTraining.procedureFields) === null || _rawTraining_procedureFields === void 0 ? void 0 : _rawTraining_procedureFields.nodes)) {\n                setRawTraining({\n                    ...rawTraining,\n                    procedureFields: {\n                        ...rawTraining.procedureFields,\n                        nodes: [\n                            ...rawTraining.procedureFields.nodes,\n                            data\n                        ]\n                    }\n                });\n            } else {\n                console.error(\"createCustomisedComponentFieldData error\", response);\n            }\n        },\n        onError: (error)=>{\n            console.error(\"createCustomisedComponentFieldData error\", error);\n        }\n    });\n    const [updateCustomisedComponentFieldData] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_30__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_8__.UPDATE_CUSTOMISED_COMPONENT_FIELD_DATA, {\n        onCompleted: (response)=>{\n            const data = response.updateCustomisedComponentFieldData;\n            if (data.id > 0) {\n                var _rawTraining_procedureFields;\n                setRawTraining({\n                    ...rawTraining,\n                    procedureFields: {\n                        ...rawTraining.procedureFields,\n                        nodes: [\n                            ...rawTraining === null || rawTraining === void 0 ? void 0 : (_rawTraining_procedureFields = rawTraining.procedureFields) === null || _rawTraining_procedureFields === void 0 ? void 0 : _rawTraining_procedureFields.nodes.filter((procedureField)=>procedureField.customisedComponentFieldID !== data.customisedComponentFieldID),\n                            {\n                                ...data\n                            }\n                        ]\n                    }\n                });\n            } else {\n                console.error(\"updateCustomisedComponentFieldData error\", response);\n            }\n        },\n        onError: (error)=>{\n            console.error(\"updateCustomisedComponentFieldData error\", error);\n        }\n    });\n    const handleSave = async ()=>{\n        var _training_Members, _training_TrainingTypes;\n        let hasErrors = false;\n        let errors = {\n            TrainingTypes: \"\",\n            TrainerID: \"\",\n            VesselID: \"\",\n            Date: \"\"\n        };\n        setFormErrors(errors);\n        if (lodash_isEmpty__WEBPACK_IMPORTED_MODULE_2___default()(training.TrainingTypes)) {\n            hasErrors = true;\n            errors.TrainingTypes = \"Nature of training is required\";\n        }\n        if (!(training.TrainerID && training.TrainerID > 0)) {\n            hasErrors = true;\n            errors.TrainerID = \"Trainer is required\";\n        }\n        if (!training.VesselID && !(training.TrainingLocationID && training.TrainingLocationID >= 0)) {\n            hasErrors = true;\n            errors.VesselID = \"Location is required\";\n        }\n        if (typeof training.Date === \"undefined\") {\n            training.Date = dayjs__WEBPACK_IMPORTED_MODULE_1___default()().format(\"YYYY-MM-DD\");\n        }\n        if (training.Date === null || !dayjs__WEBPACK_IMPORTED_MODULE_1___default()(training.Date).isValid()) {\n            hasErrors = true;\n            errors.Date = \"The date is invalid\";\n        }\n        if (hasErrors) {\n            setHasFormErrors(true);\n            setFormErrors(errors);\n            toast({\n                title: \"Error\",\n                description: errors.TrainingTypes || errors.TrainerID || errors.VesselID || errors.Date,\n                variant: \"destructive\"\n            });\n            return;\n        }\n        const input = {\n            id: trainingID,\n            date: training.Date ? dayjs__WEBPACK_IMPORTED_MODULE_1___default()(training.Date).format(\"YYYY-MM-DD\") : \"\",\n            members: (_training_Members = training.Members) === null || _training_Members === void 0 ? void 0 : _training_Members.join(\",\"),\n            trainerID: training.TrainerID,\n            trainingSummary: content,\n            trainingTypes: (_training_TrainingTypes = training.TrainingTypes) === null || _training_TrainingTypes === void 0 ? void 0 : _training_TrainingTypes.join(\",\"),\n            vesselID: training === null || training === void 0 ? void 0 : training.VesselID,\n            trainingLocationType: (training === null || training === void 0 ? void 0 : training.VesselID) ? training.VesselID === \"Other\" || training.VesselID === \"Onshore\" ? training.VesselID : \"Vessel\" : \"Location\"\n        };\n        if (trainingID === 0) {\n            await mutationCreateTrainingSession({\n                variables: {\n                    input: input\n                }\n            });\n        } else {\n            await mutationUpdateTrainingSession({\n                variables: {\n                    input: input\n                }\n            });\n        }\n    };\n    // var signatureCount = 0\n    const updateSignatures = (TrainingID)=>{\n        signatureMembers.length > 0 && (signatureMembers === null || signatureMembers === void 0 ? void 0 : signatureMembers.forEach((signature)=>{\n            checkAndSaveSignature(signature, TrainingID);\n        }));\n    };\n    const checkAndSaveSignature = async (signature, TrainingID)=>{\n        await queryGetMemberTrainingSignatures({\n            variables: {\n                filter: {\n                    memberID: {\n                        eq: signature.MemberID\n                    },\n                    trainingSessionID: {\n                        in: TrainingID\n                    }\n                }\n            }\n        }).then((response)=>{\n            const data = response.data.readMemberTraining_Signatures.nodes;\n            if (data.length > 0) {\n                mutationUpdateMemberTrainingSignature({\n                    variables: {\n                        input: {\n                            id: data[0].id,\n                            memberID: signature.MemberID,\n                            signatureData: signature.SignatureData,\n                            trainingSessionID: TrainingID\n                        }\n                    }\n                });\n            } else {\n                if (signature.SignatureData) {\n                    mutationCreateMemberTrainingSignature({\n                        variables: {\n                            input: {\n                                memberID: signature.MemberID,\n                                signatureData: signature.SignatureData,\n                                trainingSessionID: TrainingID\n                            }\n                        }\n                    });\n                }\n            }\n        }).catch((error)=>{\n            console.error(\"mutationGetMemberTrainingSignatures error\", error);\n        });\n    };\n    const [queryGetMemberTrainingSignatures] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_29__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_9__.GET_MEMBER_TRAINING_SIGNATURES);\n    const [mutationUpdateMemberTrainingSignature, { loading: mutationUpdateMemberTrainingSignatureLoading }] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_30__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_8__.UPDATE_MEMBER_TRAINING_SIGNATURE, {\n        onCompleted: (response)=>{\n            const data = response.updateMemberTraining_Signature;\n            if (data.id > 0) {\n            // signatureCount++\n            // if (signatureCount === signatureMembers.length) {\n            // }\n            } else {\n                console.error(\"mutationUpdateMemberTrainingSignature error\", response);\n            }\n        },\n        onError: (error)=>{\n            console.error(\"mutationUpdateMemberTrainingSignature error\", error);\n        }\n    });\n    const [mutationCreateMemberTrainingSignature, { loading: mutationCreateMemberTrainingSignatureLoading }] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_30__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_8__.CREATE_MEMBER_TRAINING_SIGNATURE, {\n        onCompleted: (response)=>{\n            const data = response.createMemberTraining_Signature;\n            if (data.id > 0) {\n            // signatureCount++\n            // if (signatureCount === signatureMembers.length) {\n            // }\n            } else {\n                console.error(\"mutationCreateMemberTrainingSignature error\", response);\n            }\n        },\n        onError: (error)=>{\n            console.error(\"mutationCreateMemberTrainingSignature error\", error);\n        }\n    });\n    const handleTrainingDateChange = (date)=>{\n        setTrainingDate(date && new Date(date.toString()));\n        setTraining({\n            ...training,\n            Date: dayjs__WEBPACK_IMPORTED_MODULE_1___default()(date).format(\"YYYY-MM-DD\")\n        });\n    };\n    const handleTrainerChange = (trainer)=>{\n        if (!trainer) return; // Add early return if trainer is null\n        // Use Set() to prevent duplicate values, then Array.from() to convert it to an array\n        const membersSet = new Set((training === null || training === void 0 ? void 0 : training.Members) || []);\n        membersSet.add(trainer.value);\n        const members = Array.from(membersSet);\n        setTraining({\n            ...training,\n            TrainerID: trainer.value,\n            Members: members\n        });\n        setSelectedMemberList([\n            ...selectedMemberList,\n            trainer\n        ]);\n        setSignatureMembers([\n            ...signatureMembers,\n            {\n                MemberID: +trainer.value,\n                SignatureData: null\n            }\n        ]);\n    };\n    const handleTrainingTypeChange = (trainingTypes)=>{\n        setTraining({\n            ...training,\n            TrainingTypes: trainingTypes.map((item)=>item.value)\n        });\n    };\n    /* const handleTrainingLocationChange = (vessel: any) => {\r\n        setTraining({\r\n            ...training,\r\n            VesselID: vessel.isVessel ? vessel.value : 0,\r\n            TrainingLocationID: !vessel.isVessel ? vessel.value : 0,\r\n        })\r\n    } */ const handleMemberChange = (members)=>{\n        console.log(\"\\uD83D\\uDD27 TrainingForm - handleMemberChange called with:\", {\n            members,\n            membersLength: members === null || members === void 0 ? void 0 : members.length,\n            membersType: typeof members\n        });\n        const signatures = signatureMembers.filter((item)=>members.some((m)=>+m.value === item.MemberID));\n        const memberIds = members.map((item)=>item.value);\n        console.log(\"\\uD83D\\uDD27 TrainingForm - extracted member IDs:\", memberIds);\n        setTraining({\n            ...training,\n            Members: memberIds\n        });\n        setSelectedMemberList(members);\n        setSignatureMembers(signatures);\n    };\n    const onSignatureChanged = (e, member, memberId)=>{\n        const index = signatureMembers.findIndex((object)=>object.MemberID === memberId);\n        const updatedMembers = [\n            ...signatureMembers\n        ];\n        if (e) {\n            if (index !== -1) {\n                if (e.trim() === \"\") {\n                    updatedMembers.splice(index, 1);\n                } else {\n                    updatedMembers[index].SignatureData = e;\n                }\n            } else {\n                updatedMembers.push({\n                    MemberID: memberId,\n                    SignatureData: e\n                });\n            }\n        } else {\n            updatedMembers.splice(index, 1);\n        }\n        setSignatureMembers(updatedMembers);\n    };\n    const handleTrainingVesselChange = (vessel)=>{\n        setTraining({\n            ...training,\n            VesselID: vessel ? typeof vessel === \"object\" && !Array.isArray(vessel) ? vessel.value : 0 : 0\n        });\n    };\n    const handleEditorChange = (newContent)=>{\n        setContent(newContent);\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)(()=>{\n        if (!lodash_isEmpty__WEBPACK_IMPORTED_MODULE_2___default()(training)) {\n            const vid = vesselId > 0 || isNaN(parseInt(training === null || training === void 0 ? void 0 : training.VesselID, 10)) ? vesselId : parseInt(training === null || training === void 0 ? void 0 : training.VesselID, 10);\n            setVesselID(vid);\n        }\n    }, [\n        vesselId,\n        training\n    ]);\n    const [permissions, setPermissions] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)(()=>{\n        setPermissions(_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_12__.getPermissions);\n    }, []);\n    if (!permissions || !(0,_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_12__.hasPermission)(\"EDIT_TRAINING\", permissions) && !(0,_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_12__.hasPermission)(\"VIEW_TRAINING\", permissions) && !(0,_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_12__.hasPermission)(\"RECORD_TRAINING\", permissions)) {\n        return !permissions ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_loading__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {}, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n            lineNumber: 912,\n            columnNumber: 13\n        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_loading__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n            errorMessage: \"Oops You do not have the permission to view this section.\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n            lineNumber: 914,\n            columnNumber: 13\n        }, undefined);\n    }\n    const procedures = (0,react__WEBPACK_IMPORTED_MODULE_3__.useMemo)(()=>{\n        const filteredProcedures = trainingTypes.filter((type)=>{\n            var _training_TrainingTypes;\n            return training === null || training === void 0 ? void 0 : (_training_TrainingTypes = training.TrainingTypes) === null || _training_TrainingTypes === void 0 ? void 0 : _training_TrainingTypes.includes(type.id);\n        });\n        return filteredProcedures.map((type)=>{\n            var _this;\n            return type.customisedComponentField.nodes.length > 0 ? {\n                id: type.id,\n                title: type.title,\n                fields: (_this = [\n                    ...type.customisedComponentField.nodes\n                ]) === null || _this === void 0 ? void 0 : _this.sort((a, b)=>a.sortOrder - b.sortOrder)\n            } : null;\n        }).filter((type)=>type != null);\n    }, [\n        trainingTypes,\n        training === null || training === void 0 ? void 0 : training.TrainingTypes\n    ]);\n    const vesselOptions = (0,react__WEBPACK_IMPORTED_MODULE_3__.useMemo)(()=>{\n        if (!vessels) return [];\n        return vessels.map((vessel)=>({\n                label: vessel.label,\n                value: vessel.value\n            }));\n    }, [\n        vessels\n    ]);\n    const crewValue = (0,react__WEBPACK_IMPORTED_MODULE_3__.useMemo)(()=>{\n        return memberId > 0 ? [\n            memberId.toString()\n        ] : training === null || training === void 0 ? void 0 : training.Members;\n    }, [\n        memberId,\n        training === null || training === void 0 ? void 0 : training.Members\n    ]);\n    const handleProcedureChecks = (field, type, status)=>{\n        // Always use buffer system for consistency, whether creating or updating\n        const procedureCheck = bufferProcedureCheck.filter((procedureField)=>procedureField.fieldId !== field.id);\n        setBufferProcedureCheck([\n            ...procedureCheck,\n            {\n                fieldId: field.id,\n                status: status\n            }\n        ]);\n    };\n    const getFieldStatus = (field)=>{\n        var _rawTraining_procedureFields_nodes, _rawTraining_procedureFields;\n        if (bufferProcedureCheck.length > 0) {\n            const fieldStatus = bufferProcedureCheck.find((procedureField)=>procedureField.fieldId == field.id);\n            if (fieldStatus) {\n                return fieldStatus.status ? \"Ok\" : \"Not_Ok\";\n            }\n        }\n        const fieldStatus = rawTraining === null || rawTraining === void 0 ? void 0 : (_rawTraining_procedureFields = rawTraining.procedureFields) === null || _rawTraining_procedureFields === void 0 ? void 0 : (_rawTraining_procedureFields_nodes = _rawTraining_procedureFields.nodes) === null || _rawTraining_procedureFields_nodes === void 0 ? void 0 : _rawTraining_procedureFields_nodes.find((procedureField)=>procedureField.customisedComponentFieldID == field.id);\n        return (fieldStatus === null || fieldStatus === void 0 ? void 0 : fieldStatus.status) || \"\";\n    };\n    const showCommentPopup = (field)=>{\n        var _rawTraining_procedureFields_nodes, _rawTraining_procedureFields;\n        const fieldComment = rawTraining === null || rawTraining === void 0 ? void 0 : (_rawTraining_procedureFields = rawTraining.procedureFields) === null || _rawTraining_procedureFields === void 0 ? void 0 : (_rawTraining_procedureFields_nodes = _rawTraining_procedureFields.nodes) === null || _rawTraining_procedureFields_nodes === void 0 ? void 0 : _rawTraining_procedureFields_nodes.find((procedureField)=>procedureField.customisedComponentFieldID == field.id);\n        if (bufferFieldComment.length > 0) {\n            const fieldComment = bufferFieldComment.find((procedureField)=>procedureField.fieldId == field.id);\n            setCurrentComment((fieldComment === null || fieldComment === void 0 ? void 0 : fieldComment.comment) || \"\");\n        } else {\n            setCurrentComment((fieldComment === null || fieldComment === void 0 ? void 0 : fieldComment.comment) || \"\");\n        }\n        setCurrentField(field);\n        setOpenCommentAlert(true);\n    };\n    const getComment = (field)=>{\n        var _rawTraining_procedureFields_nodes, _rawTraining_procedureFields;\n        if (bufferFieldComment.length > 0) {\n            const fieldComment = bufferFieldComment.find((procedureField)=>procedureField.fieldId == field.id);\n            if (fieldComment) {\n                return fieldComment.comment;\n            }\n        }\n        const fieldComment = rawTraining === null || rawTraining === void 0 ? void 0 : (_rawTraining_procedureFields = rawTraining.procedureFields) === null || _rawTraining_procedureFields === void 0 ? void 0 : (_rawTraining_procedureFields_nodes = _rawTraining_procedureFields.nodes) === null || _rawTraining_procedureFields_nodes === void 0 ? void 0 : _rawTraining_procedureFields_nodes.find((procedureField)=>procedureField.customisedComponentFieldID == field.id);\n        return (fieldComment === null || fieldComment === void 0 ? void 0 : fieldComment.comment) || field.comment;\n    };\n    const handleSaveComment = ()=>{\n        // Always use buffer system for consistency, whether creating or updating\n        const fieldComment = bufferFieldComment.filter((procedureField)=>procedureField.fieldId !== currentField.id);\n        setBufferFieldComment([\n            ...fieldComment,\n            {\n                fieldId: currentField.id,\n                comment: currentComment\n            }\n        ]);\n        setOpenCommentAlert(false);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_16__.Card, {\n                className: \"mb-2.5 mx-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_16__.CardHeader, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_16__.CardTitle, {\n                            children: [\n                                trainingID === 0 ? \"New\" : \"Edit\",\n                                \" Training Session\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                            lineNumber: 1027,\n                            columnNumber: 21\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                        lineNumber: 1026,\n                        columnNumber: 17\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_17__.Separator, {\n                        className: \"my-5\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                        lineNumber: 1031,\n                        columnNumber: 17\n                    }, undefined),\n                    !training && trainingID > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_skeletons__WEBPACK_IMPORTED_MODULE_5__.TrainingSessionFormSkeleton, {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                        lineNumber: 1033,\n                        columnNumber: 21\n                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_16__.CardContent, {\n                        className: \"p-0\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid lg:grid-cols-3 gap-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"col-span-3 md:col-span-1 my-4 \",\n                                        children: [\n                                            \"Training Details\",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \" mt-4 max-w-[25rem] leading-loose mb-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                                lineNumber: 1039,\n                                                columnNumber: 33\n                                            }, undefined),\n                                            training && trainingTypes.filter((type)=>{\n                                                var _training_TrainingTypes;\n                                                return (training === null || training === void 0 ? void 0 : (_training_TrainingTypes = training.TrainingTypes) === null || _training_TrainingTypes === void 0 ? void 0 : _training_TrainingTypes.includes(type.id)) && type.procedure;\n                                            }).length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_19__.Button, {\n                                                onClick: ()=>setOpenViewProcedure(true),\n                                                children: \"View Procedures\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                                lineNumber: 1047,\n                                                columnNumber: 41\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                        lineNumber: 1037,\n                                        columnNumber: 29\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"col-span-3 md:col-span-2 pt-8 pb-5 space-y-6 px-7 border border-border border-dashed rounded-lg\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-full\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-full my-4 flex flex-col\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_14__.Label, {\n                                                                label: \"Trainer\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_filter_components_crew_dropdown_crew_dropdown__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                    value: training === null || training === void 0 ? void 0 : training.TrainerID,\n                                                                    vesselID: vesselID,\n                                                                    onChange: handleTrainerChange\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                                                    lineNumber: 1059,\n                                                                    columnNumber: 45\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                                                lineNumber: 1058,\n                                                                columnNumber: 41\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"small\", {\n                                                                className: \"text-destructive\",\n                                                                children: hasFormErrors && formErrors.TrainerID\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                                                lineNumber: 1065,\n                                                                columnNumber: 41\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                                        lineNumber: 1057,\n                                                        columnNumber: 37\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-full md:mt-4 flex flex-col\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_type_multiselect_dropdown__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                                value: training === null || training === void 0 ? void 0 : training.TrainingTypes,\n                                                                onChange: handleTrainingTypeChange\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                                                lineNumber: 1071,\n                                                                columnNumber: 41\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"small\", {\n                                                                className: \"text-red-vivid-500\",\n                                                                children: hasFormErrors && formErrors.TrainingTypes\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                                                lineNumber: 1075,\n                                                                columnNumber: 41\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                                        lineNumber: 1070,\n                                                        columnNumber: 37\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                                lineNumber: 1056,\n                                                columnNumber: 33\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-full mt-4 flex flex-col\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_14__.Label, {\n                                                        children: \"Crew\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                                        lineNumber: 1082,\n                                                        columnNumber: 37\n                                                    }, undefined),\n                                                    (()=>{\n                                                        const crewValue = memberId > 0 ? [\n                                                            memberId.toString()\n                                                        ] : training === null || training === void 0 ? void 0 : training.Members;\n                                                        console.log(\"\\uD83D\\uDD27 TrainingForm - CrewMultiSelectDropdown props:\", {\n                                                            memberId,\n                                                            trainingMembers: training === null || training === void 0 ? void 0 : training.Members,\n                                                            crewValue,\n                                                            vesselID\n                                                        });\n                                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_crew_multiselect_dropdown_multiselect_dropdown__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                            value: crewValue,\n                                                            vesselID: vesselID,\n                                                            onChange: handleMemberChange\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                                            lineNumber: 1099,\n                                                            columnNumber: 45\n                                                        }, undefined);\n                                                    })()\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                                lineNumber: 1081,\n                                                columnNumber: 33\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_17__.Separator, {\n                                                className: \"my-5\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                                lineNumber: 1107,\n                                                columnNumber: 33\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex w-full gap-4 mt-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-full \",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_DateRange__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                mode: \"single\",\n                                                                onChange: handleTrainingDateChange,\n                                                                value: new Date(trainingDate)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                                                lineNumber: 1114,\n                                                                columnNumber: 41\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"small\", {\n                                                                className: \"text-destructive\",\n                                                                children: hasFormErrors && formErrors.Date\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                                                lineNumber: 1119,\n                                                                columnNumber: 41\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                                        lineNumber: 1109,\n                                                        columnNumber: 37\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-full\",\n                                                        children: [\n                                                            vessels && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_comboBox__WEBPACK_IMPORTED_MODULE_15__.Combobox, {\n                                                                options: vesselOptions,\n                                                                defaultValues: (rawTraining === null || rawTraining === void 0 ? void 0 : rawTraining.trainingLocationType) === \"Vessel\" ? rawTraining === null || rawTraining === void 0 ? void 0 : (_rawTraining_VesselID = rawTraining.VesselID) === null || _rawTraining_VesselID === void 0 ? void 0 : _rawTraining_VesselID.toString() : (rawTraining === null || rawTraining === void 0 ? void 0 : rawTraining.trainingLocationType) === \"Onshore\" ? \"Desktop/shore\" : (rawTraining === null || rawTraining === void 0 ? void 0 : rawTraining.trainingLocationType) === \"Other\" ? \"Other\" : (rawTraining === null || rawTraining === void 0 ? void 0 : rawTraining.trainingLocationType) === \"Location\" && (rawTraining === null || rawTraining === void 0 ? void 0 : (_rawTraining_trainingLocation = rawTraining.trainingLocation) === null || _rawTraining_trainingLocation === void 0 ? void 0 : _rawTraining_trainingLocation.id) > 0 ? rawTraining === null || rawTraining === void 0 ? void 0 : (_rawTraining_trainingLocation1 = rawTraining.trainingLocation) === null || _rawTraining_trainingLocation1 === void 0 ? void 0 : _rawTraining_trainingLocation1.id.toString() : vesselId ? {\n                                                                    label: (_vessels_find = vessels.find((vessel)=>vessel.value === vesselId)) === null || _vessels_find === void 0 ? void 0 : _vessels_find.label,\n                                                                    value: vesselId.toString()\n                                                                } : null,\n                                                                isLoading: rawTraining,\n                                                                onChange: handleTrainingVesselChange,\n                                                                placeholder: \"Select location\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                                                lineNumber: 1125,\n                                                                columnNumber: 45\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"small\", {\n                                                                className: \"text-destructive\",\n                                                                children: hasFormErrors && formErrors.VesselID\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                                                lineNumber: 1164,\n                                                                columnNumber: 41\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                                        lineNumber: 1123,\n                                                        columnNumber: 37\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                                lineNumber: 1108,\n                                                columnNumber: 33\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"col-span-3 md:col-span-2\",\n                                                children: [\n                                                    procedures.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-8\",\n                                                        children: procedures.map((type)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_daily_check_field__WEBPACK_IMPORTED_MODULE_22__.CheckField, {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_14__.Label, {\n                                                                        label: type.title\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                                                        lineNumber: 1184,\n                                                                        columnNumber: 53\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_daily_check_field__WEBPACK_IMPORTED_MODULE_22__.CheckFieldContent, {\n                                                                        children: type.fields.map((field)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_daily_check_field__WEBPACK_IMPORTED_MODULE_22__.DailyCheckField, {\n                                                                                displayField: field.status === \"Required\",\n                                                                                displayDescription: field.description,\n                                                                                displayLabel: field.fieldName,\n                                                                                inputId: field.id,\n                                                                                handleNoChange: ()=>handleProcedureChecks(field, type, false),\n                                                                                defaultNoChecked: getFieldStatus(field) === \"Not_Ok\",\n                                                                                handleYesChange: ()=>handleProcedureChecks(field, type, true),\n                                                                                defaultYesChecked: getFieldStatus(field) === \"Ok\",\n                                                                                commentAction: ()=>showCommentPopup(field),\n                                                                                comment: getComment(field),\n                                                                                displayImage: trainingID > 0,\n                                                                                fieldImages: fieldImages,\n                                                                                onImageUpload: refreshImages,\n                                                                                sectionData: {\n                                                                                    id: trainingID,\n                                                                                    sectionName: \"trainingSessionID\"\n                                                                                }\n                                                                            }, field.id, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                                                                lineNumber: 1188,\n                                                                                columnNumber: 65\n                                                                            }, undefined))\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                                                        lineNumber: 1185,\n                                                                        columnNumber: 53\n                                                                    }, undefined)\n                                                                ]\n                                                            }, type.id, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                                                lineNumber: 1183,\n                                                                columnNumber: 49\n                                                            }, undefined))\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                                        lineNumber: 1181,\n                                                        columnNumber: 41\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"my-4 flex items-center w-full\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_editor__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                            id: \"TrainingSummary\",\n                                                            placeholder: \"Summary of training, identify any outcomes, further training required or other observations.\",\n                                                            className: \"!w-full  ring-1 ring-inset \",\n                                                            handleEditorChange: handleEditorChange,\n                                                            content: content\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                                            lineNumber: 1263,\n                                                            columnNumber: 41\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                                        lineNumber: 1262,\n                                                        columnNumber: 37\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                                lineNumber: 1179,\n                                                columnNumber: 33\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                        lineNumber: 1055,\n                                        columnNumber: 29\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                lineNumber: 1036,\n                                columnNumber: 25\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_17__.Separator, {\n                                className: \"my-5\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                lineNumber: 1284,\n                                columnNumber: 25\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid lg:grid-cols-3 gap-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"col-span-3 md:col-span-1 md:my-4 \",\n                                        children: \"Signatures\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                        lineNumber: 1286,\n                                        columnNumber: 29\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"col-span-3 sm:col-span-2 md:my-4 flex justify-between flex-wrap gap-4\",\n                                        children: selectedMemberList && selectedMemberList.map((member, index)=>{\n                                            var _signatureMembers_find;\n                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-full md:w-96\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_signature_pad__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                    className: \"w-full\",\n                                                    member: member.label,\n                                                    memberId: member.value,\n                                                    onSignatureChanged: (signature, member, memberId)=>onSignatureChanged(signature, member !== null && member !== void 0 ? member : \"\", memberId || 0),\n                                                    signature: {\n                                                        id: (_signatureMembers_find = signatureMembers.find((sig)=>sig.MemberID === member.value)) === null || _signatureMembers_find === void 0 ? void 0 : _signatureMembers_find.ID\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                                    lineNumber: 1296,\n                                                    columnNumber: 49\n                                                }, undefined)\n                                            }, index, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                                lineNumber: 1293,\n                                                columnNumber: 45\n                                            }, undefined);\n                                        })\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                        lineNumber: 1289,\n                                        columnNumber: 29\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                lineNumber: 1285,\n                                columnNumber: 25\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                        lineNumber: 1035,\n                        columnNumber: 21\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                lineNumber: 1025,\n                columnNumber: 13\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_footer_wrapper__WEBPACK_IMPORTED_MODULE_21__.FooterWrapper, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_19__.Button, {\n                        variant: \"back\",\n                        onClick: ()=>router.push(\"/crew-training\"),\n                        iconLeft: _barrel_optimize_names_ArrowLeft_lucide_react__WEBPACK_IMPORTED_MODULE_31__[\"default\"],\n                        children: \"Cancel\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                        lineNumber: 1328,\n                        columnNumber: 17\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_19__.Button, {\n                        onClick: handleSave,\n                        disabled: mutationCreateTrainingSessionLoading || mutationUpdateTrainingSessionLoading,\n                        children: trainingID === 0 ? \"Create session\" : \"Update session\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                        lineNumber: 1340,\n                        columnNumber: 17\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                lineNumber: 1327,\n                columnNumber: 13\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sheet__WEBPACK_IMPORTED_MODULE_18__.Sheet, {\n                open: openViewProcedure,\n                onOpenChange: setOpenViewProcedure,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sheet__WEBPACK_IMPORTED_MODULE_18__.SheetContent, {\n                    side: \"right\",\n                    className: \"w-[400px] sm:w-[540px]\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sheet__WEBPACK_IMPORTED_MODULE_18__.SheetHeader, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sheet__WEBPACK_IMPORTED_MODULE_18__.SheetTitle, {\n                                children: \"Procedures\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                lineNumber: 1352,\n                                columnNumber: 25\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                            lineNumber: 1351,\n                            columnNumber: 21\n                        }, undefined),\n                        training && trainingTypes.filter((type)=>{\n                            var _training_TrainingTypes;\n                            return (training === null || training === void 0 ? void 0 : (_training_TrainingTypes = training.TrainingTypes) === null || _training_TrainingTypes === void 0 ? void 0 : _training_TrainingTypes.includes(type.id)) && type.procedure;\n                        }).map((type)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-4 px-2.5 sm:px-5\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_25__.H4, {\n                                        children: type.title\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                        lineNumber: 1367,\n                                        columnNumber: 37\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        dangerouslySetInnerHTML: {\n                                            __html: type.procedure\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                        lineNumber: 1369,\n                                        columnNumber: 37\n                                    }, undefined)\n                                ]\n                            }, type.id, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                lineNumber: 1364,\n                                columnNumber: 33\n                            }, undefined))\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                    lineNumber: 1350,\n                    columnNumber: 17\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                lineNumber: 1349,\n                columnNumber: 13\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert_dialog__WEBPACK_IMPORTED_MODULE_26__.AlertDialog, {\n                open: openCommentAlert,\n                onOpenChange: setOpenCommentAlert,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert_dialog__WEBPACK_IMPORTED_MODULE_26__.AlertDialogContent, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert_dialog__WEBPACK_IMPORTED_MODULE_26__.AlertDialogHeader, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert_dialog__WEBPACK_IMPORTED_MODULE_26__.AlertDialogTitle, {\n                                    children: \"Add Comment\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                    lineNumber: 1383,\n                                    columnNumber: 25\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert_dialog__WEBPACK_IMPORTED_MODULE_26__.AlertDialogDescription, {\n                                    children: \"Add a comment for this procedure check.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                    lineNumber: 1384,\n                                    columnNumber: 25\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                            lineNumber: 1382,\n                            columnNumber: 21\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_27__.Textarea, {\n                            value: currentComment,\n                            onChange: (e)=>setCurrentComment(e.target.value),\n                            placeholder: \"Enter your comment here...\",\n                            rows: 4\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                            lineNumber: 1388,\n                            columnNumber: 21\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert_dialog__WEBPACK_IMPORTED_MODULE_26__.AlertDialogFooter, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert_dialog__WEBPACK_IMPORTED_MODULE_26__.AlertDialogCancel, {\n                                    children: \"Cancel\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                    lineNumber: 1395,\n                                    columnNumber: 25\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert_dialog__WEBPACK_IMPORTED_MODULE_26__.AlertDialogAction, {\n                                    onClick: handleSaveComment,\n                                    children: \"Save Comment\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                    lineNumber: 1396,\n                                    columnNumber: 25\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                            lineNumber: 1394,\n                            columnNumber: 21\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                    lineNumber: 1381,\n                    columnNumber: 17\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                lineNumber: 1378,\n                columnNumber: 13\n            }, undefined)\n        ]\n    }, void 0, true);\n};\n_s(TrainingForm, \"X7x734SkOtQ7exNspuDixtT+/6k=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_10__.useRouter,\n        _hooks_use_toast__WEBPACK_IMPORTED_MODULE_24__.useToast,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_29__.useLazyQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_29__.useLazyQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_29__.useLazyQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_29__.useLazyQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_29__.useLazyQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_30__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_30__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_29__.useLazyQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_30__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_30__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_30__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_30__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_29__.useLazyQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_30__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_30__.useMutation\n    ];\n});\n_c = TrainingForm;\n/* harmony default export */ __webpack_exports__[\"default\"] = (TrainingForm);\nvar _c;\n$RefreshReg$(_c, \"TrainingForm\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/ui/crew-training/create/form.tsx\n"));

/***/ })

});