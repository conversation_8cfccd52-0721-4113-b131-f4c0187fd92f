"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/crew-training/create/page",{

/***/ "(app-pages-browser)/./src/app/ui/crew/multiselect-dropdown/multiselect-dropdown.tsx":
/*!***********************************************************************!*\
  !*** ./src/app/ui/crew/multiselect-dropdown/multiselect-dropdown.tsx ***!
  \***********************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var lodash_isEmpty__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! lodash/isEmpty */ \"(app-pages-browser)/./node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/isEmpty.js\");\n/* harmony import */ var lodash_isEmpty__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(lodash_isEmpty__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/app/lib/graphQL/mutation */ \"(app-pages-browser)/./src/app/lib/graphQL/mutation/index.ts\");\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @apollo/client */ \"(app-pages-browser)/./node_modules/.pnpm/@apollo+client@3.13.8_@type_3a2c516023e671829487810ec4a9ea8f/node_modules/@apollo/client/react/hooks/useLazyQuery.js\");\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @apollo/client */ \"(app-pages-browser)/./node_modules/.pnpm/@apollo+client@3.13.8_@type_3a2c516023e671829487810ec4a9ea8f/node_modules/@apollo/client/react/hooks/useMutation.js\");\n/* harmony import */ var _app_offline_models_seaLogsMember__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/app/offline/models/seaLogsMember */ \"(app-pages-browser)/./src/app/offline/models/seaLogsMember.js\");\n/* harmony import */ var _app_offline_helpers_functions__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/app/offline/helpers/functions */ \"(app-pages-browser)/./src/app/offline/helpers/functions.ts\");\n/* harmony import */ var _components_ui_comboBox__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/comboBox */ \"(app-pages-browser)/./src/components/ui/comboBox.tsx\");\n/* harmony import */ var _add_crew_member_dialog__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../add-crew-member-dialog */ \"(app-pages-browser)/./src/app/ui/crew/add-crew-member-dialog.tsx\");\n/* harmony import */ var _queries__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./queries */ \"(app-pages-browser)/./src/app/ui/crew/multiselect-dropdown/queries.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nconst CrewMultiSelectDropdown = (param)=>{\n    let { value = [], onChange, memberIdOptions = [], departments = [], filterByAdmin = false, offline = false, vesselID = 0 } = param;\n    _s();\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [crewList, setCrewList] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [openCreateMemberDialog, setOpenCreateMemberDialog] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedIDs, setSelectedIDs] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const seaLogsMemberModel = new _app_offline_models_seaLogsMember__WEBPACK_IMPORTED_MODULE_4__[\"default\"]();\n    const handleSetCrewList = (crewListRaw)=>{\n        console.log(\"\\uD83D\\uDD27 CrewMultiSelectDropdown - handleSetCrewList called with:\", {\n            crewListRawLength: crewListRaw === null || crewListRaw === void 0 ? void 0 : crewListRaw.length,\n            vesselID,\n            departments: departments.length,\n            memberIdOptions: memberIdOptions.length\n        });\n        // If vesselID > 0, filter the crew list to display only the vessel crew.\n        const vesselCrewList = vesselID > 0 ? crewListRaw.filter((crew)=>crew.vehicles.nodes.some((vehicle)=>+vehicle.id === vesselID)) : crewListRaw;\n        console.log(\"\\uD83D\\uDD27 CrewMultiSelectDropdown - vesselCrewList filtered:\", {\n            originalLength: crewListRaw === null || crewListRaw === void 0 ? void 0 : crewListRaw.length,\n            filteredLength: vesselCrewList === null || vesselCrewList === void 0 ? void 0 : vesselCrewList.length\n        });\n        const createOption = {\n            value: \"newCrewMember\",\n            label: \"--- Create Crew Member ---\"\n        };\n        const data = vesselCrewList.filter((crew)=>filterByAdmin ? !crewIsAdmin(crew) : true);\n        if (departments.length > 0) {\n            const departmentList = departments.flatMap((department)=>{\n                return department.id;\n            });\n            const crews = data.filter((crew)=>crew.departments.nodes.some((node)=>departmentList.includes(node.id))).map((item)=>{\n                var _item_firstName, _item_surname;\n                return {\n                    value: item.id,\n                    label: \"\".concat((_item_firstName = item.firstName) !== null && _item_firstName !== void 0 ? _item_firstName : \"\", \" \").concat((_item_surname = item.surname) !== null && _item_surname !== void 0 ? _item_surname : \"\"),\n                    profile: {\n                        firstName: item.firstName,\n                        surname: item.surname,\n                        avatar: null\n                    }\n                };\n            });\n            if (memberIdOptions.length === 0) {\n                setCrewList([\n                    createOption,\n                    ...crews\n                ]);\n            } else {\n                const filteredCrewList = crews.filter((crew)=>{\n                    return memberIdOptions.includes(crew.value);\n                });\n                setCrewList(filteredCrewList);\n            }\n        } else {\n            const crews = data.map((item)=>{\n                var _item_firstName, _item_surname;\n                return {\n                    value: item.id,\n                    label: \"\".concat((_item_firstName = item.firstName) !== null && _item_firstName !== void 0 ? _item_firstName : \"\", \" \").concat((_item_surname = item.surname) !== null && _item_surname !== void 0 ? _item_surname : \"\"),\n                    profile: {\n                        firstName: item.firstName,\n                        surname: item.surname,\n                        avatar: null\n                    }\n                };\n            });\n            if (memberIdOptions.length === 0) {\n                setCrewList([\n                    createOption,\n                    ...crews\n                ]);\n            } else {\n                const filteredCrewList = crews.filter((crew)=>{\n                    return memberIdOptions.includes(crew.value);\n                });\n                setCrewList(filteredCrewList);\n            }\n        }\n        console.log(\"\\uD83D\\uDD27 CrewMultiSelectDropdown - final crewList set:\", {\n            crewListLength: crewList.length,\n            sampleCrew: crewList[0]\n        });\n    };\n    const [querySeaLogsMembersList] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_9__.useLazyQuery)(_queries__WEBPACK_IMPORTED_MODULE_8__.ReadSeaLogsMembers, {\n        fetchPolicy: \"cache-and-network\",\n        onError: (error)=>{\n            console.error(\"querySeaLogsMembersList error\", error);\n        }\n    });\n    const loadCrewMembers = async ()=>{\n        let allMembers = [];\n        let offset = 0;\n        const limit = 100;\n        let hasNextPage = true;\n        try {\n            while(hasNextPage){\n                var _response_data;\n                const response = await querySeaLogsMembersList({\n                    variables: {\n                        filter: {\n                            isArchived: {\n                                eq: false\n                            }\n                        },\n                        limit: limit,\n                        offset: offset\n                    }\n                });\n                if ((_response_data = response.data) === null || _response_data === void 0 ? void 0 : _response_data.readSeaLogsMembers) {\n                    const data = response.data.readSeaLogsMembers.nodes;\n                    const pageInfo = response.data.readSeaLogsMembers.pageInfo;\n                    if (data && data.length > 0) {\n                        allMembers = [\n                            ...allMembers,\n                            ...data\n                        ];\n                    }\n                    hasNextPage = (pageInfo === null || pageInfo === void 0 ? void 0 : pageInfo.hasNextPage) || false;\n                    offset += limit;\n                } else {\n                    hasNextPage = false;\n                }\n            }\n            // Set all collected members at once\n            if (allMembers.length > 0) {\n                handleSetCrewList(allMembers);\n            }\n        } catch (error) {\n            console.error(\"Error loading all crew members:\", error);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (isLoading && !offline) {\n            loadCrewMembers();\n            setIsLoading(false);\n        }\n    }, [\n        isLoading,\n        offline\n    ]);\n    // if (!offline) {\n    // getSeaLogsMembersList(handleSetCrewList)\n    // }\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (offline) {\n            seaLogsMemberModel.getAll().then((data)=>{\n                handleSetCrewList(data);\n            });\n        }\n    }, [\n        offline\n    ]);\n    const crewIsAdmin = (crew)=>{\n        var _crew_groups_nodes;\n        return ((_crew_groups_nodes = crew.groups.nodes) === null || _crew_groups_nodes === void 0 ? void 0 : _crew_groups_nodes.filter((permission)=>{\n            return permission.code === \"admin\";\n        }).length) > 0;\n    };\n    const handleOnChange = (value)=>{\n        if (!value) {\n            setSelectedIDs([]);\n            onChange([]);\n            return;\n        }\n        const valueArray = Array.isArray(value) ? value : [\n            value\n        ];\n        // Fix the condition to properly check for 'newCrewMember'\n        if (valueArray.find((option)=>option.value === \"newCrewMember\")) {\n            setOpenCreateMemberDialog(true);\n            return;\n        }\n        if (valueArray.length === 0) {\n            setSelectedIDs([]);\n            onChange([]);\n            return;\n        }\n        // Ensure we're working with valid Option objects that have both value and label\n        const validOptions = valueArray.filter((option)=>option && typeof option === \"object\" && option.value !== undefined && option.label !== null && option.label !== undefined);\n        setSelectedIDs(validOptions);\n        onChange(validOptions);\n    };\n    const [queryAddMember] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_10__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_3__.CREATE_USER, {\n        fetchPolicy: \"no-cache\",\n        onCompleted: (response)=>{\n            const data = response.createSeaLogsMember;\n            if (data.id > 0) {\n                setOpenCreateMemberDialog(false);\n                const newData = {\n                    value: data.id,\n                    label: data.firstName + \" \" + data.surname\n                };\n                setCrewList([\n                    ...crewList,\n                    newData\n                ]);\n                const newSelectedIDs = [\n                    ...selectedIDs,\n                    newData\n                ];\n                setSelectedIDs(newSelectedIDs);\n                onChange(newSelectedIDs);\n                setError(false);\n            }\n        },\n        onError: (error)=>{\n            console.error(\"createUser error\", error.message);\n            setError(error);\n        }\n    });\n    const handleAddNewMember = async ()=>{\n        const variables = {\n            input: {\n                firstName: document.getElementById(\"crew-firstName\").value ? document.getElementById(\"crew-firstName\").value : null,\n                surname: document.getElementById(\"crew-surname\").value ? document.getElementById(\"crew-surname\").value : null,\n                email: document.getElementById(\"crew-email\").value ? document.getElementById(\"crew-email\").value : null,\n                phoneNumber: document.getElementById(\"crew-phoneNumber\").value ? document.getElementById(\"crew-phoneNumber\").value : null,\n                username: document.getElementById(\"crew-username\").value ? document.getElementById(\"crew-username\").value : null,\n                password: document.getElementById(\"crew-password\").value ? document.getElementById(\"crew-password\").value : null\n            }\n        };\n        if (offline) {\n            // queryAddMember\n            const data = await seaLogsMemberModel.save({\n                ...variables.input,\n                id: (0,_app_offline_helpers_functions__WEBPACK_IMPORTED_MODULE_5__.generateUniqueId)()\n            });\n            setOpenCreateMemberDialog(false);\n            const newData = {\n                value: data.id,\n                label: data.firstName + \" \" + data.surname\n            };\n            setCrewList([\n                ...crewList,\n                newData\n            ]);\n            const newSelectedIDs = [\n                ...selectedIDs,\n                newData\n            ];\n            setSelectedIDs(newSelectedIDs);\n            onChange(newSelectedIDs);\n            setError(false);\n        } else {\n            await queryAddMember({\n                variables: variables\n            });\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!lodash_isEmpty__WEBPACK_IMPORTED_MODULE_2___default()(crewList) && !lodash_isEmpty__WEBPACK_IMPORTED_MODULE_2___default()(value)) {\n            // Convert value array to proper Option objects\n            const selectedOptions = value.map((id)=>{\n                const option = crewList.find((crew)=>crew.value === id);\n                return option;\n            }).filter(Boolean) // Remove any undefined/null options\n            ;\n            setSelectedIDs(selectedOptions);\n        } else if (lodash_isEmpty__WEBPACK_IMPORTED_MODULE_2___default()(value)) {\n            // Clear selection when value is empty\n            setSelectedIDs([]);\n        }\n    }, [\n        value,\n        crewList\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_comboBox__WEBPACK_IMPORTED_MODULE_6__.Combobox, {\n                options: crewList,\n                value: selectedIDs,\n                onChange: handleOnChange,\n                placeholder: \"Select Crew\",\n                multi: true,\n                isLoading: isLoading || crewList.length === 0\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\multiselect-dropdown\\\\multiselect-dropdown.tsx\",\n                lineNumber: 363,\n                columnNumber: 13\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_add_crew_member_dialog__WEBPACK_IMPORTED_MODULE_7__.AddCrewMemberDialog, {\n                openDialog: openCreateMemberDialog,\n                setOpenDialog: setOpenCreateMemberDialog,\n                handleCreate: handleAddNewMember,\n                actionText: \"Add Crew Member\",\n                error: error\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\multiselect-dropdown\\\\multiselect-dropdown.tsx\",\n                lineNumber: 371,\n                columnNumber: 13\n            }, undefined)\n        ]\n    }, void 0, true);\n};\n_s(CrewMultiSelectDropdown, \"B5kTBW1ijKbUdQ1QBKs5KkaV5Fg=\", false, function() {\n    return [\n        _apollo_client__WEBPACK_IMPORTED_MODULE_9__.useLazyQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_10__.useMutation\n    ];\n});\n_c = CrewMultiSelectDropdown;\n/* harmony default export */ __webpack_exports__[\"default\"] = (CrewMultiSelectDropdown);\nvar _c;\n$RefreshReg$(_c, \"CrewMultiSelectDropdown\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/ui/crew/multiselect-dropdown/multiselect-dropdown.tsx\n"));

/***/ })

});