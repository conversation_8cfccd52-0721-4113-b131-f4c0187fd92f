"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/crew-training/create/page",{

/***/ "(app-pages-browser)/./src/app/ui/crew-training/create/form.tsx":
/*!**************************************************!*\
  !*** ./src/app/ui/crew-training/create/form.tsx ***!
  \**************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! dayjs */ \"(app-pages-browser)/./node_modules/.pnpm/dayjs@1.11.13/node_modules/dayjs/dayjs.min.js\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(dayjs__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var lodash_isEmpty__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! lodash/isEmpty */ \"(app-pages-browser)/./node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/isEmpty.js\");\n/* harmony import */ var lodash_isEmpty__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(lodash_isEmpty__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _components_filter_components_crew_dropdown_crew_dropdown__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../../../components/filter/components/crew-dropdown/crew-dropdown */ \"(app-pages-browser)/./src/components/filter/components/crew-dropdown/crew-dropdown.tsx\");\n/* harmony import */ var _components_skeletons__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../../../components/skeletons */ \"(app-pages-browser)/./src/components/skeletons.tsx\");\n/* harmony import */ var _type_multiselect_dropdown__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../type-multiselect-dropdown */ \"(app-pages-browser)/./src/app/ui/crew-training/type-multiselect-dropdown.tsx\");\n/* harmony import */ var _crew_multiselect_dropdown_multiselect_dropdown__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../../crew/multiselect-dropdown/multiselect-dropdown */ \"(app-pages-browser)/./src/app/ui/crew/multiselect-dropdown/multiselect-dropdown.tsx\");\n/* harmony import */ var _app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/app/lib/graphQL/mutation */ \"(app-pages-browser)/./src/app/lib/graphQL/mutation/index.ts\");\n/* harmony import */ var _app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/app/lib/graphQL/query */ \"(app-pages-browser)/./src/app/lib/graphQL/query/index.ts\");\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_30__ = __webpack_require__(/*! @apollo/client */ \"(app-pages-browser)/./node_modules/.pnpm/@apollo+client@3.13.8_@type_3a2c516023e671829487810ec4a9ea8f/node_modules/@apollo/client/react/hooks/useLazyQuery.js\");\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_31__ = __webpack_require__(/*! @apollo/client */ \"(app-pages-browser)/./node_modules/.pnpm/@apollo+client@3.13.8_@type_3a2c516023e671829487810ec4a9ea8f/node_modules/@apollo/client/react/hooks/useMutation.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _app_lib_actions__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/app/lib/actions */ \"(app-pages-browser)/./src/app/lib/actions.tsx\");\n/* harmony import */ var _editor__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ../../editor */ \"(app-pages-browser)/./src/app/ui/editor.tsx\");\n/* harmony import */ var _app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/app/helpers/userHelper */ \"(app-pages-browser)/./src/app/helpers/userHelper.ts\");\n/* harmony import */ var _app_loading__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/app/loading */ \"(app-pages-browser)/./src/app/loading.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./src/components/ui/label.tsx\");\n/* harmony import */ var _components_ui_comboBox__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @/components/ui/comboBox */ \"(app-pages-browser)/./src/components/ui/comboBox.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_separator__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @/components/ui/separator */ \"(app-pages-browser)/./src/components/ui/separator.tsx\");\n/* harmony import */ var _components_ui_sheet__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! @/components/ui/sheet */ \"(app-pages-browser)/./src/components/ui/sheet.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_signature_pad__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! @/components/signature-pad */ \"(app-pages-browser)/./src/components/signature-pad.tsx\");\n/* harmony import */ var _components_footer_wrapper__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! @/components/footer-wrapper */ \"(app-pages-browser)/./src/components/footer-wrapper.tsx\");\n/* harmony import */ var _components_daily_check_field__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! @/components/daily-check-field */ \"(app-pages-browser)/./src/components/daily-check-field.tsx\");\n/* harmony import */ var _components_DateRange__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! @/components/DateRange */ \"(app-pages-browser)/./src/components/DateRange.tsx\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! @/hooks/use-toast */ \"(app-pages-browser)/./src/hooks/use-toast.ts\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_lucide_react__WEBPACK_IMPORTED_MODULE_32__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.474.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _components_ui__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! @/components/ui */ \"(app-pages-browser)/./src/components/ui/index.ts\");\n/* harmony import */ var _components_ui_alert_dialog__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! @/components/ui/alert-dialog */ \"(app-pages-browser)/./src/components/ui/alert-dialog.tsx\");\n/* harmony import */ var _components_ui_textarea__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! @/components/ui/textarea */ \"(app-pages-browser)/./src/components/ui/textarea.tsx\");\n/* harmony import */ var _queries__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! ./queries */ \"(app-pages-browser)/./src/app/ui/crew-training/create/queries.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst TrainingForm = (param)=>{\n    let { trainingID = 0, memberId = 0, trainingTypeId = 0, vesselId = 0 } = param;\n    var _rawTraining_VesselID, _rawTraining_trainingLocation, _rawTraining_trainingLocation1, _vessels_find;\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_10__.useRouter)();\n    const { toast } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_25__.useToast)();\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(true);\n    const [training, setTraining] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)({});\n    const [rawTraining, setRawTraining] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)();\n    const [trainingDate, setTrainingDate] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(new Date());\n    const [hasFormErrors, setHasFormErrors] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    const [selectedMemberList, setSelectedMemberList] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)([]);\n    const [signatureMembers, setSignatureMembers] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)([]);\n    const [vessels, setVessels] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)();\n    const [trainingTypes, setTrainingTypes] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)([]);\n    const [content, setContent] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(\"\");\n    const [openViewProcedure, setOpenViewProcedure] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    const [currentComment, setCurrentComment] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(\"\");\n    const [currentField, setCurrentField] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(\"\");\n    const [bufferProcedureCheck, setBufferProcedureCheck] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)([]);\n    const [bufferFieldComment, setBufferFieldComment] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)([]);\n    const [openCommentAlert, setOpenCommentAlert] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    const [formErrors, setFormErrors] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)({\n        TrainingTypes: \"\",\n        TrainerID: \"\",\n        VesselID: \"\",\n        Date: \"\"\n    });\n    const [vesselID, setVesselID] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(vesselId);\n    const [fieldImages, setFieldImages] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    const [getFieldImages] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_30__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_9__.GET_SECTION_MEMBER_IMAGES, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            const data = response.readCaptureImages.nodes;\n            if (data) {\n                setFieldImages(data);\n            }\n        },\n        onError: (error)=>{\n            console.error(\"getFieldImages error\", error);\n        }\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)(()=>{\n        getFieldImages({\n            variables: {\n                filter: {\n                    trainingSessionID: {\n                        eq: trainingID\n                    }\n                }\n            }\n        });\n    }, []);\n    const refreshImages = async ()=>{\n        await getFieldImages({\n            variables: {\n                filter: {\n                    trainingSessionID: {\n                        eq: trainingID\n                    }\n                }\n            }\n        });\n    };\n    const handleSetTraining = (training)=>{\n        var _training_procedureFields;\n        const tDate = new Date(training.date);\n        setTrainingDate(tDate);\n        const trainingData = {\n            ID: trainingID,\n            Date: dayjs__WEBPACK_IMPORTED_MODULE_1___default()(training.date).format(\"YYYY-MM-DD\"),\n            Members: training.members.nodes.map((m)=>m.id),\n            TrainerID: training.trainer.id,\n            TrainingSummary: training.trainingSummary,\n            TrainingTypes: training.trainingTypes.nodes.map((t)=>t.id),\n            // VesselID: training.vessel.id,\n            VesselID: training.vesselID\n        };\n        setRawTraining(training);\n        setTraining(trainingData);\n        setContent(training.trainingSummary);\n        const members = training.members.nodes.map((m)=>{\n            var _m_firstName, _m_surname;\n            return {\n                label: \"\".concat((_m_firstName = m.firstName) !== null && _m_firstName !== void 0 ? _m_firstName : \"\", \" \").concat((_m_surname = m.surname) !== null && _m_surname !== void 0 ? _m_surname : \"\"),\n                value: m.id\n            };\n        }) || [];\n        const vesselCrewIds = training.vessel.seaLogsMembers.nodes.map((slm)=>+slm.id);\n        const vesselCrews = members.filter((m)=>vesselCrewIds.includes(+m.value));\n        setSelectedMemberList(vesselCrews);\n        const signatures = training.signatures.nodes.map((s)=>({\n                MemberID: s.member.id,\n                SignatureData: s.signatureData,\n                ID: s.id\n            }));\n        setSignatureMembers(signatures);\n        // Initialize buffer with existing procedure field data for updates\n        if ((_training_procedureFields = training.procedureFields) === null || _training_procedureFields === void 0 ? void 0 : _training_procedureFields.nodes) {\n            const existingProcedureChecks = training.procedureFields.nodes.map((field)=>({\n                    fieldId: field.customisedComponentFieldID,\n                    status: field.status === \"Ok\"\n                }));\n            setBufferProcedureCheck(existingProcedureChecks);\n            const existingFieldComments = training.procedureFields.nodes.filter((field)=>field.comment).map((field)=>({\n                    fieldId: field.customisedComponentFieldID,\n                    comment: field.comment\n                }));\n            setBufferFieldComment(existingFieldComments);\n        }\n    };\n    const handleSetVessels = (data)=>{\n        const activeVessels = data === null || data === void 0 ? void 0 : data.filter((vessel)=>!vessel.archived);\n        const formattedData = [\n            {\n                label: \"Other\",\n                value: \"Other\"\n            },\n            {\n                label: \"Desktop/shore\",\n                value: \"Onshore\"\n            },\n            ...activeVessels.map((vessel)=>({\n                    value: vessel.id,\n                    label: vessel.title\n                }))\n        ];\n        setVessels(formattedData);\n    };\n    const [queryVessels] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_30__.useLazyQuery)(_queries__WEBPACK_IMPORTED_MODULE_29__.ReadVessels, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (queryVesselResponse)=>{\n            if (queryVesselResponse.readVessels.nodes) {\n                handleSetVessels(queryVesselResponse.readVessels.nodes);\n            }\n        },\n        onError: (error)=>{\n            console.error(\"queryVessels error\", error);\n        }\n    });\n    const loadVessels = async ()=>{\n        await queryVessels({\n            variables: {\n                limit: 200,\n                offset: 0\n            }\n        });\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)(()=>{\n        if (isLoading) {\n            loadVessels();\n            setIsLoading(false);\n        }\n    }, [\n        isLoading\n    ]);\n    (0,_app_lib_actions__WEBPACK_IMPORTED_MODULE_11__.getTrainingSessionByID)(trainingID, handleSetTraining);\n    const [mutationCreateTrainingSession, { loading: mutationCreateTrainingSessionLoading }] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_31__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_8__.CREATE_TRAINING_SESSION, {\n        onCompleted: (response)=>{\n            const data = response.createTrainingSession;\n            if (data.id > 0) {\n                if (bufferProcedureCheck.length > 0) {\n                    const procedureFields = bufferProcedureCheck.map((procedureField)=>{\n                        var _bufferFieldComment_find;\n                        return {\n                            status: procedureField.status ? \"Ok\" : \"Not_Ok\",\n                            trainingSessionID: data.id,\n                            customisedComponentFieldID: procedureField.fieldId,\n                            comment: (_bufferFieldComment_find = bufferFieldComment.find((comment)=>comment.fieldId == procedureField.fieldId)) === null || _bufferFieldComment_find === void 0 ? void 0 : _bufferFieldComment_find.comment\n                        };\n                    });\n                    procedureFields.forEach((procedureField)=>{\n                        createCustomisedComponentFieldData({\n                            variables: {\n                                input: procedureField\n                            }\n                        });\n                    });\n                }\n                updateTrainingSessionDues();\n                updateSignatures(data.id);\n                handleEditorChange(data.trainingSummary);\n                router.push(\"/crew-training\");\n            } else {\n                console.error(\"mutationCreateUser error\", response);\n            }\n        },\n        onError: (error)=>{\n            console.error(\"mutationCreateTrainingSession error\", error);\n        }\n    });\n    const [mutationUpdateTrainingSession, { loading: mutationUpdateTrainingSessionLoading }] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_31__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_8__.UPDATE_TRAINING_SESSION, {\n        onCompleted: (response)=>{\n            const data = response.updateTrainingSession;\n            if (data.id > 0) {\n                // Handle procedure checks for updates\n                if (bufferProcedureCheck.length > 0) {\n                    const procedureFields = bufferProcedureCheck.map((procedureField)=>{\n                        var _rawTraining_procedureFields_nodes, _rawTraining_procedureFields, _bufferFieldComment_find;\n                        const existingField = rawTraining === null || rawTraining === void 0 ? void 0 : (_rawTraining_procedureFields = rawTraining.procedureFields) === null || _rawTraining_procedureFields === void 0 ? void 0 : (_rawTraining_procedureFields_nodes = _rawTraining_procedureFields.nodes) === null || _rawTraining_procedureFields_nodes === void 0 ? void 0 : _rawTraining_procedureFields_nodes.find((field)=>field.customisedComponentFieldID === procedureField.fieldId);\n                        return {\n                            id: existingField === null || existingField === void 0 ? void 0 : existingField.id,\n                            status: procedureField.status ? \"Ok\" : \"Not_Ok\",\n                            trainingSessionID: data.id,\n                            customisedComponentFieldID: procedureField.fieldId,\n                            comment: (_bufferFieldComment_find = bufferFieldComment.find((comment)=>comment.fieldId == procedureField.fieldId)) === null || _bufferFieldComment_find === void 0 ? void 0 : _bufferFieldComment_find.comment\n                        };\n                    });\n                    procedureFields.forEach((procedureField)=>{\n                        if (procedureField.id) {\n                            // Update existing field\n                            updateCustomisedComponentFieldData({\n                                variables: {\n                                    input: procedureField\n                                }\n                            });\n                        } else {\n                            // Create new field\n                            const { id, ...createInput } = procedureField;\n                            createCustomisedComponentFieldData({\n                                variables: {\n                                    input: createInput\n                                }\n                            });\n                        }\n                    });\n                }\n                updateTrainingSessionDues();\n                updateSignatures(trainingID);\n                handleEditorChange(data.trainingSummary);\n                if (+memberId > 0) {\n                    router.push(\"/crew/info?id=\".concat(memberId));\n                } else if (+vesselId > 0) {\n                    router.push(\"/vessel/info?id=\".concat(vesselId));\n                } else {\n                    router.push(\"/crew-training\");\n                }\n            } else {\n                console.error(\"mutationUpdateTrainingSession error\", response);\n            }\n        },\n        onError: (error)=>{\n            console.error(\"mutationUpdateTrainingSession error\", error);\n        }\n    });\n    const [readOneTrainingSessionDue, { loading: readOneTrainingSessionDueLoading }] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_30__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_9__.READ_ONE_TRAINING_SESSION_DUE, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            return response.readOneTrainingSessionDue.data;\n        },\n        onError: (error)=>{\n            console.error(\"readOneTrainingSessionDueLoading error:\", error);\n            return null;\n        }\n    });\n    const getTrainingSessionDueWithVariables = async function() {\n        let variables = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {}, onCompleted = arguments.length > 1 ? arguments[1] : void 0;\n        const { data } = await readOneTrainingSessionDue({\n            variables: variables\n        });\n        onCompleted(data.readOneTrainingSessionDue);\n    };\n    const [mutationCreateTrainingSessionDue, { loading: createTrainingSessionDueLoading }] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_31__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_8__.CREATE_TRAINING_SESSION_DUE, {\n        onCompleted: (response)=>{},\n        onError: (error)=>{\n            console.error(\"createTrainingSessionDue error\", error);\n        }\n    });\n    const [mutationUpdateTrainingSessionDue, { loading: updateTrainingSessionDueLoading }] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_31__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_8__.UPDATE_TRAINING_SESSION_DUE, {\n        onCompleted: (response)=>{},\n        onError: (error)=>{\n            console.error(\"updateTrainingSessionDue error\", error);\n        }\n    });\n    const updateTrainingSessionDues = async ()=>{\n        const trainingSessionDues = [];\n        const vesselID = training.VesselID;\n        training.TrainingTypes.forEach((t)=>{\n            const trainingInfo = trainingTypes.find((tt)=>tt.id === t);\n            if (!lodash_isEmpty__WEBPACK_IMPORTED_MODULE_2___default()(trainingInfo) && trainingInfo.occursEvery > 0) {\n                const trainingTypeID = t;\n                const newDueDate = dayjs__WEBPACK_IMPORTED_MODULE_1___default()(training.Date).add(trainingInfo.occursEvery, \"day\");\n                training.Members.forEach((m)=>{\n                    const memberID = m;\n                    trainingSessionDues.push({\n                        dueDate: newDueDate.format(\"YYYY-MM-DD\"),\n                        memberID: memberID,\n                        vesselID: vesselID,\n                        trainingTypeID: trainingTypeID\n                    });\n                });\n            }\n        });\n        let trainingSessionDueWithIDs = [];\n        if (!lodash_isEmpty__WEBPACK_IMPORTED_MODULE_2___default()(trainingSessionDues)) {\n            await Promise.all(trainingSessionDues.map(async (item)=>{\n                const variables = {\n                    filter: {\n                        memberID: {\n                            eq: item.memberID\n                        },\n                        vesselID: {\n                            eq: item.vesselID\n                        },\n                        trainingTypeID: {\n                            eq: item.trainingTypeID\n                        }\n                    }\n                };\n                const onCompleted = (response)=>{\n                    var _response_id;\n                    trainingSessionDueWithIDs.push({\n                        ...item,\n                        id: (_response_id = response === null || response === void 0 ? void 0 : response.id) !== null && _response_id !== void 0 ? _response_id : 0\n                    });\n                };\n                await getTrainingSessionDueWithVariables(variables, onCompleted);\n            }));\n        }\n        if (!lodash_isEmpty__WEBPACK_IMPORTED_MODULE_2___default()(trainingSessionDueWithIDs)) {\n            await Promise.all(Array.from(trainingSessionDueWithIDs).map(async (item)=>{\n                const variables = {\n                    variables: {\n                        input: item\n                    }\n                };\n                if (item.id === 0) {\n                    await mutationCreateTrainingSessionDue(variables);\n                } else {\n                    await mutationUpdateTrainingSessionDue(variables);\n                }\n            }));\n        }\n    };\n    const [createCustomisedComponentFieldData] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_31__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_8__.CREATE_CUSTOMISED_COMPONENT_FIELD_DATA, {\n        onCompleted: (response)=>{\n            var _rawTraining_procedureFields;\n            const data = response.createCustomisedComponentFieldData;\n            if (data.id > 0 && (rawTraining === null || rawTraining === void 0 ? void 0 : (_rawTraining_procedureFields = rawTraining.procedureFields) === null || _rawTraining_procedureFields === void 0 ? void 0 : _rawTraining_procedureFields.nodes)) {\n                setRawTraining({\n                    ...rawTraining,\n                    procedureFields: {\n                        ...rawTraining.procedureFields,\n                        nodes: [\n                            ...rawTraining.procedureFields.nodes,\n                            data\n                        ]\n                    }\n                });\n            } else {\n                console.error(\"createCustomisedComponentFieldData error\", response);\n            }\n        },\n        onError: (error)=>{\n            console.error(\"createCustomisedComponentFieldData error\", error);\n        }\n    });\n    const [updateCustomisedComponentFieldData] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_31__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_8__.UPDATE_CUSTOMISED_COMPONENT_FIELD_DATA, {\n        onCompleted: (response)=>{\n            const data = response.updateCustomisedComponentFieldData;\n            if (data.id > 0) {\n                var _rawTraining_procedureFields;\n                setRawTraining({\n                    ...rawTraining,\n                    procedureFields: {\n                        ...rawTraining.procedureFields,\n                        nodes: [\n                            ...rawTraining === null || rawTraining === void 0 ? void 0 : (_rawTraining_procedureFields = rawTraining.procedureFields) === null || _rawTraining_procedureFields === void 0 ? void 0 : _rawTraining_procedureFields.nodes.filter((procedureField)=>procedureField.customisedComponentFieldID !== data.customisedComponentFieldID),\n                            {\n                                ...data\n                            }\n                        ]\n                    }\n                });\n            } else {\n                console.error(\"updateCustomisedComponentFieldData error\", response);\n            }\n        },\n        onError: (error)=>{\n            console.error(\"updateCustomisedComponentFieldData error\", error);\n        }\n    });\n    const handleSave = async ()=>{\n        var _training_Members, _training_TrainingTypes;\n        let hasErrors = false;\n        let errors = {\n            TrainingTypes: \"\",\n            TrainerID: \"\",\n            VesselID: \"\",\n            Date: \"\"\n        };\n        setFormErrors(errors);\n        if (lodash_isEmpty__WEBPACK_IMPORTED_MODULE_2___default()(training.TrainingTypes)) {\n            hasErrors = true;\n            errors.TrainingTypes = \"Nature of training is required\";\n        }\n        if (!(training.TrainerID && training.TrainerID > 0)) {\n            hasErrors = true;\n            errors.TrainerID = \"Trainer is required\";\n        }\n        if (!training.VesselID && !(training.TrainingLocationID && training.TrainingLocationID >= 0)) {\n            hasErrors = true;\n            errors.VesselID = \"Location is required\";\n        }\n        if (typeof training.Date === \"undefined\") {\n            training.Date = dayjs__WEBPACK_IMPORTED_MODULE_1___default()().format(\"YYYY-MM-DD\");\n        }\n        if (training.Date === null || !dayjs__WEBPACK_IMPORTED_MODULE_1___default()(training.Date).isValid()) {\n            hasErrors = true;\n            errors.Date = \"The date is invalid\";\n        }\n        if (hasErrors) {\n            setHasFormErrors(true);\n            setFormErrors(errors);\n            toast({\n                title: \"Error\",\n                description: errors.TrainingTypes || errors.TrainerID || errors.VesselID || errors.Date,\n                variant: \"destructive\"\n            });\n            return;\n        }\n        const input = {\n            id: trainingID,\n            date: training.Date ? dayjs__WEBPACK_IMPORTED_MODULE_1___default()(training.Date).format(\"YYYY-MM-DD\") : \"\",\n            members: (_training_Members = training.Members) === null || _training_Members === void 0 ? void 0 : _training_Members.join(\",\"),\n            trainerID: training.TrainerID,\n            trainingSummary: content,\n            trainingTypes: (_training_TrainingTypes = training.TrainingTypes) === null || _training_TrainingTypes === void 0 ? void 0 : _training_TrainingTypes.join(\",\"),\n            vesselID: training === null || training === void 0 ? void 0 : training.VesselID,\n            trainingLocationType: (training === null || training === void 0 ? void 0 : training.VesselID) ? training.VesselID === \"Other\" || training.VesselID === \"Onshore\" ? training.VesselID : \"Vessel\" : \"Location\"\n        };\n        if (trainingID === 0) {\n            await mutationCreateTrainingSession({\n                variables: {\n                    input: input\n                }\n            });\n        } else {\n            await mutationUpdateTrainingSession({\n                variables: {\n                    input: input\n                }\n            });\n        }\n    };\n    // var signatureCount = 0\n    const updateSignatures = (TrainingID)=>{\n        signatureMembers.length > 0 && (signatureMembers === null || signatureMembers === void 0 ? void 0 : signatureMembers.forEach((signature)=>{\n            checkAndSaveSignature(signature, TrainingID);\n        }));\n    };\n    const checkAndSaveSignature = async (signature, TrainingID)=>{\n        await queryGetMemberTrainingSignatures({\n            variables: {\n                filter: {\n                    memberID: {\n                        eq: signature.MemberID\n                    },\n                    trainingSessionID: {\n                        in: TrainingID\n                    }\n                }\n            }\n        }).then((response)=>{\n            const data = response.data.readMemberTraining_Signatures.nodes;\n            if (data.length > 0) {\n                mutationUpdateMemberTrainingSignature({\n                    variables: {\n                        input: {\n                            id: data[0].id,\n                            memberID: signature.MemberID,\n                            signatureData: signature.SignatureData,\n                            trainingSessionID: TrainingID\n                        }\n                    }\n                });\n            } else {\n                if (signature.SignatureData) {\n                    mutationCreateMemberTrainingSignature({\n                        variables: {\n                            input: {\n                                memberID: signature.MemberID,\n                                signatureData: signature.SignatureData,\n                                trainingSessionID: TrainingID\n                            }\n                        }\n                    });\n                }\n            }\n        }).catch((error)=>{\n            console.error(\"mutationGetMemberTrainingSignatures error\", error);\n        });\n    };\n    const [queryGetMemberTrainingSignatures] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_30__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_9__.GET_MEMBER_TRAINING_SIGNATURES);\n    const [mutationUpdateMemberTrainingSignature, { loading: mutationUpdateMemberTrainingSignatureLoading }] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_31__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_8__.UPDATE_MEMBER_TRAINING_SIGNATURE, {\n        onCompleted: (response)=>{\n            const data = response.updateMemberTraining_Signature;\n            if (data.id > 0) {\n            // signatureCount++\n            // if (signatureCount === signatureMembers.length) {\n            // }\n            } else {\n                console.error(\"mutationUpdateMemberTrainingSignature error\", response);\n            }\n        },\n        onError: (error)=>{\n            console.error(\"mutationUpdateMemberTrainingSignature error\", error);\n        }\n    });\n    const [mutationCreateMemberTrainingSignature, { loading: mutationCreateMemberTrainingSignatureLoading }] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_31__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_8__.CREATE_MEMBER_TRAINING_SIGNATURE, {\n        onCompleted: (response)=>{\n            const data = response.createMemberTraining_Signature;\n            if (data.id > 0) {\n            // signatureCount++\n            // if (signatureCount === signatureMembers.length) {\n            // }\n            } else {\n                console.error(\"mutationCreateMemberTrainingSignature error\", response);\n            }\n        },\n        onError: (error)=>{\n            console.error(\"mutationCreateMemberTrainingSignature error\", error);\n        }\n    });\n    const handleTrainingDateChange = (date)=>{\n        setTrainingDate(date && new Date(date.toString()));\n        setTraining({\n            ...training,\n            Date: dayjs__WEBPACK_IMPORTED_MODULE_1___default()(date).format(\"YYYY-MM-DD\")\n        });\n    };\n    const handleTrainerChange = (trainer)=>{\n        if (!trainer) return; // Add early return if trainer is null\n        // Use Set() to prevent duplicate values, then Array.from() to convert it to an array\n        const membersSet = new Set((training === null || training === void 0 ? void 0 : training.Members) || []);\n        membersSet.add(trainer.value);\n        const members = Array.from(membersSet);\n        setTraining({\n            ...training,\n            TrainerID: trainer.value,\n            Members: members\n        });\n        setSelectedMemberList([\n            ...selectedMemberList,\n            trainer\n        ]);\n        setSignatureMembers([\n            ...signatureMembers,\n            {\n                MemberID: +trainer.value,\n                SignatureData: null\n            }\n        ]);\n    };\n    const handleTrainingTypeChange = (trainingTypes)=>{\n        setTraining({\n            ...training,\n            TrainingTypes: trainingTypes.map((item)=>item.value)\n        });\n    };\n    /* const handleTrainingLocationChange = (vessel: any) => {\r\n        setTraining({\r\n            ...training,\r\n            VesselID: vessel.isVessel ? vessel.value : 0,\r\n            TrainingLocationID: !vessel.isVessel ? vessel.value : 0,\r\n        })\r\n    } */ const handleMemberChange = (members)=>{\n        console.log(\"\\uD83D\\uDD27 TrainingForm - handleMemberChange called with:\", {\n            members,\n            membersLength: members === null || members === void 0 ? void 0 : members.length,\n            membersType: typeof members\n        });\n        const signatures = signatureMembers.filter((item)=>members.some((m)=>+m.value === item.MemberID));\n        const memberIds = members.map((item)=>item.value);\n        console.log(\"\\uD83D\\uDD27 TrainingForm - extracted member IDs:\", memberIds);\n        setTraining({\n            ...training,\n            Members: memberIds\n        });\n        setSelectedMemberList(members);\n        setSignatureMembers(signatures);\n    };\n    const onSignatureChanged = (e, member, memberId)=>{\n        const index = signatureMembers.findIndex((object)=>object.MemberID === memberId);\n        const updatedMembers = [\n            ...signatureMembers\n        ];\n        if (e) {\n            if (index !== -1) {\n                if (e.trim() === \"\") {\n                    updatedMembers.splice(index, 1);\n                } else {\n                    updatedMembers[index].SignatureData = e;\n                }\n            } else {\n                updatedMembers.push({\n                    MemberID: memberId,\n                    SignatureData: e\n                });\n            }\n        } else {\n            updatedMembers.splice(index, 1);\n        }\n        setSignatureMembers(updatedMembers);\n    };\n    (0,_app_lib_actions__WEBPACK_IMPORTED_MODULE_11__.getTrainingTypeByID)(trainingTypeId, setTraining);\n    const handleTrainingVesselChange = (vessel)=>{\n        setTraining({\n            ...training,\n            VesselID: vessel ? typeof vessel === \"object\" && !Array.isArray(vessel) ? vessel.value : 0 : 0\n        });\n    };\n    const handleEditorChange = (newContent)=>{\n        setContent(newContent);\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)(()=>{\n        if (!lodash_isEmpty__WEBPACK_IMPORTED_MODULE_2___default()(training)) {\n            const vid = vesselId > 0 || isNaN(parseInt(training === null || training === void 0 ? void 0 : training.VesselID, 10)) ? vesselId : parseInt(training === null || training === void 0 ? void 0 : training.VesselID, 10);\n            setVesselID(vid);\n        }\n    }, [\n        vesselId,\n        training\n    ]);\n    const [permissions, setPermissions] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)(()=>{\n        setPermissions(_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_13__.getPermissions);\n    }, []);\n    if (!permissions || !(0,_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_13__.hasPermission)(\"EDIT_TRAINING\", permissions) && !(0,_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_13__.hasPermission)(\"VIEW_TRAINING\", permissions) && !(0,_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_13__.hasPermission)(\"RECORD_TRAINING\", permissions)) {\n        return !permissions ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_loading__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {}, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n            lineNumber: 848,\n            columnNumber: 13\n        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_loading__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n            errorMessage: \"Oops You do not have the permission to view this section.\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n            lineNumber: 850,\n            columnNumber: 13\n        }, undefined);\n    }\n    const getProcedures = ()=>{\n        const procedures = trainingTypes.filter((type)=>{\n            var _training_TrainingTypes;\n            return training === null || training === void 0 ? void 0 : (_training_TrainingTypes = training.TrainingTypes) === null || _training_TrainingTypes === void 0 ? void 0 : _training_TrainingTypes.includes(type.id);\n        });\n        return procedures.map((type)=>{\n            var _this;\n            return type.customisedComponentField.nodes.length > 0 ? {\n                id: type.id,\n                title: type.title,\n                fields: (_this = [\n                    ...type.customisedComponentField.nodes\n                ]) === null || _this === void 0 ? void 0 : _this.sort((a, b)=>a.sortOrder - b.sortOrder)\n            } : null;\n        }).filter((type)=>type != null);\n    };\n    const handleProcedureChecks = (field, type, status)=>{\n        // Always use buffer system for consistency, whether creating or updating\n        const procedureCheck = bufferProcedureCheck.filter((procedureField)=>procedureField.fieldId !== field.id);\n        setBufferProcedureCheck([\n            ...procedureCheck,\n            {\n                fieldId: field.id,\n                status: status\n            }\n        ]);\n    };\n    const getFieldStatus = (field)=>{\n        var _rawTraining_procedureFields_nodes, _rawTraining_procedureFields;\n        if (bufferProcedureCheck.length > 0) {\n            const fieldStatus = bufferProcedureCheck.find((procedureField)=>procedureField.fieldId == field.id);\n            if (fieldStatus) {\n                return fieldStatus.status ? \"Ok\" : \"Not_Ok\";\n            }\n        }\n        const fieldStatus = rawTraining === null || rawTraining === void 0 ? void 0 : (_rawTraining_procedureFields = rawTraining.procedureFields) === null || _rawTraining_procedureFields === void 0 ? void 0 : (_rawTraining_procedureFields_nodes = _rawTraining_procedureFields.nodes) === null || _rawTraining_procedureFields_nodes === void 0 ? void 0 : _rawTraining_procedureFields_nodes.find((procedureField)=>procedureField.customisedComponentFieldID == field.id);\n        return (fieldStatus === null || fieldStatus === void 0 ? void 0 : fieldStatus.status) || \"\";\n    };\n    const showCommentPopup = (field)=>{\n        var _rawTraining_procedureFields_nodes, _rawTraining_procedureFields;\n        const fieldComment = rawTraining === null || rawTraining === void 0 ? void 0 : (_rawTraining_procedureFields = rawTraining.procedureFields) === null || _rawTraining_procedureFields === void 0 ? void 0 : (_rawTraining_procedureFields_nodes = _rawTraining_procedureFields.nodes) === null || _rawTraining_procedureFields_nodes === void 0 ? void 0 : _rawTraining_procedureFields_nodes.find((procedureField)=>procedureField.customisedComponentFieldID == field.id);\n        if (bufferFieldComment.length > 0) {\n            const fieldComment = bufferFieldComment.find((procedureField)=>procedureField.fieldId == field.id);\n            setCurrentComment((fieldComment === null || fieldComment === void 0 ? void 0 : fieldComment.comment) || \"\");\n        } else {\n            setCurrentComment((fieldComment === null || fieldComment === void 0 ? void 0 : fieldComment.comment) || \"\");\n        }\n        setCurrentField(field);\n        setOpenCommentAlert(true);\n    };\n    const getComment = (field)=>{\n        var _rawTraining_procedureFields_nodes, _rawTraining_procedureFields;\n        if (bufferFieldComment.length > 0) {\n            const fieldComment = bufferFieldComment.find((procedureField)=>procedureField.fieldId == field.id);\n            if (fieldComment) {\n                return fieldComment.comment;\n            }\n        }\n        const fieldComment = rawTraining === null || rawTraining === void 0 ? void 0 : (_rawTraining_procedureFields = rawTraining.procedureFields) === null || _rawTraining_procedureFields === void 0 ? void 0 : (_rawTraining_procedureFields_nodes = _rawTraining_procedureFields.nodes) === null || _rawTraining_procedureFields_nodes === void 0 ? void 0 : _rawTraining_procedureFields_nodes.find((procedureField)=>procedureField.customisedComponentFieldID == field.id);\n        return (fieldComment === null || fieldComment === void 0 ? void 0 : fieldComment.comment) || field.comment;\n    };\n    const handleSaveComment = ()=>{\n        // Always use buffer system for consistency, whether creating or updating\n        const fieldComment = bufferFieldComment.filter((procedureField)=>procedureField.fieldId !== currentField.id);\n        setBufferFieldComment([\n            ...fieldComment,\n            {\n                fieldId: currentField.id,\n                comment: currentComment\n            }\n        ]);\n        setOpenCommentAlert(false);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_17__.Card, {\n                className: \"mb-2.5 mx-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_17__.CardHeader, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_17__.CardTitle, {\n                            children: [\n                                trainingID === 0 ? \"New\" : \"Edit\",\n                                \" Training Session\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                            lineNumber: 951,\n                            columnNumber: 21\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                        lineNumber: 950,\n                        columnNumber: 17\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_18__.Separator, {\n                        className: \"my-5\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                        lineNumber: 955,\n                        columnNumber: 17\n                    }, undefined),\n                    !training && trainingID > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_skeletons__WEBPACK_IMPORTED_MODULE_5__.TrainingSessionFormSkeleton, {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                        lineNumber: 957,\n                        columnNumber: 21\n                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_17__.CardContent, {\n                        className: \"p-0\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid lg:grid-cols-3 gap-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"col-span-3 md:col-span-1 my-4 \",\n                                        children: [\n                                            \"Training Details\",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \" mt-4 max-w-[25rem] leading-loose mb-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                                lineNumber: 963,\n                                                columnNumber: 33\n                                            }, undefined),\n                                            training && trainingTypes.filter((type)=>{\n                                                var _training_TrainingTypes;\n                                                return (training === null || training === void 0 ? void 0 : (_training_TrainingTypes = training.TrainingTypes) === null || _training_TrainingTypes === void 0 ? void 0 : _training_TrainingTypes.includes(type.id)) && type.procedure;\n                                            }).length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_20__.Button, {\n                                                onClick: ()=>setOpenViewProcedure(true),\n                                                children: \"View Procedures\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                                lineNumber: 971,\n                                                columnNumber: 41\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                        lineNumber: 961,\n                                        columnNumber: 29\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"col-span-3 md:col-span-2 pt-8 pb-5 space-y-6 px-7 border border-border border-dashed rounded-lg\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-full\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-full my-4 flex flex-col\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_15__.Label, {\n                                                                label: \"Trainer\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_filter_components_crew_dropdown_crew_dropdown__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                    value: training === null || training === void 0 ? void 0 : training.TrainerID,\n                                                                    vesselID: vesselID,\n                                                                    onChange: handleTrainerChange\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                                                    lineNumber: 983,\n                                                                    columnNumber: 45\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                                                lineNumber: 982,\n                                                                columnNumber: 41\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"small\", {\n                                                                className: \"text-destructive\",\n                                                                children: hasFormErrors && formErrors.TrainerID\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                                                lineNumber: 989,\n                                                                columnNumber: 41\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                                        lineNumber: 981,\n                                                        columnNumber: 37\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-full md:mt-4 flex flex-col\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_type_multiselect_dropdown__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                                value: training === null || training === void 0 ? void 0 : training.TrainingTypes,\n                                                                onChange: handleTrainingTypeChange\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                                                lineNumber: 995,\n                                                                columnNumber: 41\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"small\", {\n                                                                className: \"text-red-vivid-500\",\n                                                                children: hasFormErrors && formErrors.TrainingTypes\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                                                lineNumber: 999,\n                                                                columnNumber: 41\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                                        lineNumber: 994,\n                                                        columnNumber: 37\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                                lineNumber: 980,\n                                                columnNumber: 33\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-full mt-4 flex flex-col\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_15__.Label, {\n                                                        children: \"Crew\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                                        lineNumber: 1006,\n                                                        columnNumber: 37\n                                                    }, undefined),\n                                                    (()=>{\n                                                        const crewValue = memberId > 0 ? [\n                                                            memberId.toString()\n                                                        ] : training === null || training === void 0 ? void 0 : training.Members;\n                                                        console.log(\"\\uD83D\\uDD27 TrainingForm - CrewMultiSelectDropdown props:\", {\n                                                            memberId,\n                                                            trainingMembers: training === null || training === void 0 ? void 0 : training.Members,\n                                                            crewValue,\n                                                            vesselID\n                                                        });\n                                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_crew_multiselect_dropdown_multiselect_dropdown__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                            value: crewValue,\n                                                            vesselID: vesselID,\n                                                            onChange: handleMemberChange\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                                            lineNumber: 1023,\n                                                            columnNumber: 45\n                                                        }, undefined);\n                                                    })()\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                                lineNumber: 1005,\n                                                columnNumber: 33\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_18__.Separator, {\n                                                className: \"my-5\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                                lineNumber: 1031,\n                                                columnNumber: 33\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex w-full gap-4 mt-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-full \",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_DateRange__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                mode: \"single\",\n                                                                onChange: handleTrainingDateChange,\n                                                                value: new Date(trainingDate)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                                                lineNumber: 1038,\n                                                                columnNumber: 41\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"small\", {\n                                                                className: \"text-destructive\",\n                                                                children: hasFormErrors && formErrors.Date\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                                                lineNumber: 1043,\n                                                                columnNumber: 41\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                                        lineNumber: 1033,\n                                                        columnNumber: 37\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-full\",\n                                                        children: [\n                                                            vessels && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_comboBox__WEBPACK_IMPORTED_MODULE_16__.Combobox, {\n                                                                options: vessels.map((vessel)=>({\n                                                                        label: vessel.label,\n                                                                        value: vessel.value\n                                                                    })),\n                                                                defaultValues: (rawTraining === null || rawTraining === void 0 ? void 0 : rawTraining.trainingLocationType) === \"Vessel\" ? rawTraining === null || rawTraining === void 0 ? void 0 : (_rawTraining_VesselID = rawTraining.VesselID) === null || _rawTraining_VesselID === void 0 ? void 0 : _rawTraining_VesselID.toString() : (rawTraining === null || rawTraining === void 0 ? void 0 : rawTraining.trainingLocationType) === \"Onshore\" ? \"Desktop/shore\" : (rawTraining === null || rawTraining === void 0 ? void 0 : rawTraining.trainingLocationType) === \"Other\" ? \"Other\" : (rawTraining === null || rawTraining === void 0 ? void 0 : rawTraining.trainingLocationType) === \"Location\" && (rawTraining === null || rawTraining === void 0 ? void 0 : (_rawTraining_trainingLocation = rawTraining.trainingLocation) === null || _rawTraining_trainingLocation === void 0 ? void 0 : _rawTraining_trainingLocation.id) > 0 ? rawTraining === null || rawTraining === void 0 ? void 0 : (_rawTraining_trainingLocation1 = rawTraining.trainingLocation) === null || _rawTraining_trainingLocation1 === void 0 ? void 0 : _rawTraining_trainingLocation1.id.toString() : vesselId ? {\n                                                                    label: (_vessels_find = vessels.find((vessel)=>vessel.value === vesselId)) === null || _vessels_find === void 0 ? void 0 : _vessels_find.label,\n                                                                    value: vesselId.toString()\n                                                                } : null,\n                                                                isLoading: rawTraining,\n                                                                onChange: handleTrainingVesselChange,\n                                                                placeholder: \"Select location\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                                                lineNumber: 1049,\n                                                                columnNumber: 45\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"small\", {\n                                                                className: \"text-destructive\",\n                                                                children: hasFormErrors && formErrors.VesselID\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                                                lineNumber: 1093,\n                                                                columnNumber: 41\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                                        lineNumber: 1047,\n                                                        columnNumber: 37\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                                lineNumber: 1032,\n                                                columnNumber: 33\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"col-span-3 md:col-span-2\",\n                                                children: [\n                                                    getProcedures().length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-8\",\n                                                        children: getProcedures().map((type)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_daily_check_field__WEBPACK_IMPORTED_MODULE_23__.CheckField, {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_15__.Label, {\n                                                                        label: type.title\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                                                        lineNumber: 1114,\n                                                                        columnNumber: 57\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_daily_check_field__WEBPACK_IMPORTED_MODULE_23__.CheckFieldContent, {\n                                                                        children: type.fields.map((field)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_daily_check_field__WEBPACK_IMPORTED_MODULE_23__.DailyCheckField, {\n                                                                                displayField: field.status === \"Required\",\n                                                                                displayDescription: field.description,\n                                                                                displayLabel: field.fieldName,\n                                                                                inputId: field.id,\n                                                                                handleNoChange: ()=>handleProcedureChecks(field, type, false),\n                                                                                defaultNoChecked: getFieldStatus(field) === \"Not_Ok\",\n                                                                                handleYesChange: ()=>handleProcedureChecks(field, type, true),\n                                                                                defaultYesChecked: getFieldStatus(field) === \"Ok\",\n                                                                                commentAction: ()=>showCommentPopup(field),\n                                                                                comment: getComment(field),\n                                                                                displayImage: trainingID > 0,\n                                                                                fieldImages: fieldImages,\n                                                                                onImageUpload: refreshImages,\n                                                                                sectionData: {\n                                                                                    id: trainingID,\n                                                                                    sectionName: \"trainingSessionID\"\n                                                                                }\n                                                                            }, field.id, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                                                                lineNumber: 1122,\n                                                                                columnNumber: 69\n                                                                            }, undefined))\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                                                        lineNumber: 1117,\n                                                                        columnNumber: 57\n                                                                    }, undefined)\n                                                                ]\n                                                            }, type.id, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                                                lineNumber: 1113,\n                                                                columnNumber: 53\n                                                            }, undefined))\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                                        lineNumber: 1110,\n                                                        columnNumber: 41\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"my-4 flex items-center w-full\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_editor__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                            id: \"TrainingSummary\",\n                                                            placeholder: \"Summary of training, identify any outcomes, further training required or other observations.\",\n                                                            className: \"!w-full  ring-1 ring-inset \",\n                                                            handleEditorChange: handleEditorChange,\n                                                            content: content\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                                            lineNumber: 1198,\n                                                            columnNumber: 41\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                                        lineNumber: 1197,\n                                                        columnNumber: 37\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                                lineNumber: 1108,\n                                                columnNumber: 33\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                        lineNumber: 979,\n                                        columnNumber: 29\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                lineNumber: 960,\n                                columnNumber: 25\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_18__.Separator, {\n                                className: \"my-5\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                lineNumber: 1219,\n                                columnNumber: 25\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid lg:grid-cols-3 gap-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"col-span-3 md:col-span-1 md:my-4 \",\n                                        children: \"Signatures\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                        lineNumber: 1221,\n                                        columnNumber: 29\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"col-span-3 sm:col-span-2 md:my-4 flex justify-between flex-wrap gap-4\",\n                                        children: selectedMemberList && selectedMemberList.map((member, index)=>{\n                                            var _signatureMembers_find;\n                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-full md:w-96\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_signature_pad__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                    className: \"w-full\",\n                                                    member: member.label,\n                                                    memberId: member.value,\n                                                    onSignatureChanged: (signature, member, memberId)=>onSignatureChanged(signature, member !== null && member !== void 0 ? member : \"\", memberId || 0),\n                                                    signature: {\n                                                        id: (_signatureMembers_find = signatureMembers.find((sig)=>sig.MemberID === member.value)) === null || _signatureMembers_find === void 0 ? void 0 : _signatureMembers_find.ID\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                                    lineNumber: 1231,\n                                                    columnNumber: 49\n                                                }, undefined)\n                                            }, index, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                                lineNumber: 1228,\n                                                columnNumber: 45\n                                            }, undefined);\n                                        })\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                        lineNumber: 1224,\n                                        columnNumber: 29\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                lineNumber: 1220,\n                                columnNumber: 25\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                        lineNumber: 959,\n                        columnNumber: 21\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                lineNumber: 949,\n                columnNumber: 13\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_footer_wrapper__WEBPACK_IMPORTED_MODULE_22__.FooterWrapper, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_20__.Button, {\n                        variant: \"back\",\n                        onClick: ()=>router.push(\"/crew-training\"),\n                        iconLeft: _barrel_optimize_names_ArrowLeft_lucide_react__WEBPACK_IMPORTED_MODULE_32__[\"default\"],\n                        children: \"Cancel\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                        lineNumber: 1263,\n                        columnNumber: 17\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_20__.Button, {\n                        onClick: handleSave,\n                        disabled: mutationCreateTrainingSessionLoading || mutationUpdateTrainingSessionLoading,\n                        children: trainingID === 0 ? \"Create session\" : \"Update session\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                        lineNumber: 1275,\n                        columnNumber: 17\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                lineNumber: 1262,\n                columnNumber: 13\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sheet__WEBPACK_IMPORTED_MODULE_19__.Sheet, {\n                open: openViewProcedure,\n                onOpenChange: setOpenViewProcedure,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sheet__WEBPACK_IMPORTED_MODULE_19__.SheetContent, {\n                    side: \"right\",\n                    className: \"w-[400px] sm:w-[540px]\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sheet__WEBPACK_IMPORTED_MODULE_19__.SheetHeader, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sheet__WEBPACK_IMPORTED_MODULE_19__.SheetTitle, {\n                                children: \"Procedures\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                lineNumber: 1287,\n                                columnNumber: 25\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                            lineNumber: 1286,\n                            columnNumber: 21\n                        }, undefined),\n                        training && trainingTypes.filter((type)=>{\n                            var _training_TrainingTypes;\n                            return (training === null || training === void 0 ? void 0 : (_training_TrainingTypes = training.TrainingTypes) === null || _training_TrainingTypes === void 0 ? void 0 : _training_TrainingTypes.includes(type.id)) && type.procedure;\n                        }).map((type)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-4 px-2.5 sm:px-5\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_26__.H4, {\n                                        children: type.title\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                        lineNumber: 1302,\n                                        columnNumber: 37\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        dangerouslySetInnerHTML: {\n                                            __html: type.procedure\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                        lineNumber: 1304,\n                                        columnNumber: 37\n                                    }, undefined)\n                                ]\n                            }, type.id, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                lineNumber: 1299,\n                                columnNumber: 33\n                            }, undefined))\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                    lineNumber: 1285,\n                    columnNumber: 17\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                lineNumber: 1284,\n                columnNumber: 13\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert_dialog__WEBPACK_IMPORTED_MODULE_27__.AlertDialog, {\n                open: openCommentAlert,\n                onOpenChange: setOpenCommentAlert,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert_dialog__WEBPACK_IMPORTED_MODULE_27__.AlertDialogContent, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert_dialog__WEBPACK_IMPORTED_MODULE_27__.AlertDialogHeader, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert_dialog__WEBPACK_IMPORTED_MODULE_27__.AlertDialogTitle, {\n                                    children: \"Add Comment\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                    lineNumber: 1318,\n                                    columnNumber: 25\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert_dialog__WEBPACK_IMPORTED_MODULE_27__.AlertDialogDescription, {\n                                    children: \"Add a comment for this procedure check.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                    lineNumber: 1319,\n                                    columnNumber: 25\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                            lineNumber: 1317,\n                            columnNumber: 21\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_28__.Textarea, {\n                            value: currentComment,\n                            onChange: (e)=>setCurrentComment(e.target.value),\n                            placeholder: \"Enter your comment here...\",\n                            rows: 4\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                            lineNumber: 1323,\n                            columnNumber: 21\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert_dialog__WEBPACK_IMPORTED_MODULE_27__.AlertDialogFooter, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert_dialog__WEBPACK_IMPORTED_MODULE_27__.AlertDialogCancel, {\n                                    children: \"Cancel\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                    lineNumber: 1330,\n                                    columnNumber: 25\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert_dialog__WEBPACK_IMPORTED_MODULE_27__.AlertDialogAction, {\n                                    onClick: handleSaveComment,\n                                    children: \"Save Comment\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                    lineNumber: 1331,\n                                    columnNumber: 25\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                            lineNumber: 1329,\n                            columnNumber: 21\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                    lineNumber: 1316,\n                    columnNumber: 17\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                lineNumber: 1313,\n                columnNumber: 13\n            }, undefined)\n        ]\n    }, void 0, true);\n};\n_s(TrainingForm, \"Kpk999jA4FZirJIja2nOanr46Ls=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_10__.useRouter,\n        _hooks_use_toast__WEBPACK_IMPORTED_MODULE_25__.useToast,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_30__.useLazyQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_30__.useLazyQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_31__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_31__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_30__.useLazyQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_31__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_31__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_31__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_31__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_30__.useLazyQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_31__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_31__.useMutation\n    ];\n});\n_c = TrainingForm;\n/* harmony default export */ __webpack_exports__[\"default\"] = (TrainingForm);\nvar _c;\n$RefreshReg$(_c, \"TrainingForm\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/ui/crew-training/create/form.tsx\n"));

/***/ })

});