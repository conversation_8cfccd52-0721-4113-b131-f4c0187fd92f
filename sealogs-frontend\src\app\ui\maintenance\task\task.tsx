'use client'
import React, { useEffect, useState } from 'react'
import { useLazyQuery, useMutation } from '@apollo/client'
import {
    GET_CREW_BY_IDS,
    GET_ENGINE_IDS_BY_VESSEL,
    GET_ENGINES,
    GET_FILES,
    GET_MAINTENANCE_CHECK,
    GET_MAINTENANCE_CHECK_SUBTASK,
    GET_RECURRING_TASK,
    GetTaskRecords,
    GetMaintenanceCategories,
    GET_INVENTORY_LIST,
    VESSEL_STATUS,
    GET_SECTION_MEMBER_IMAGES,
} from '@/app/lib/graphQL/query'
import {
    UPDATE_COMPONENT_MAINTENANCE_CHECK,
    CREATE_SEALOGS_FILE_LINKS,
    DELETE_COMPONENT_MAINTENANCE_CHECK,
    UPDATE_COMPONENT_MAINTENANCE_SCHEDULE,
    CREATE_COMPONENT_MAINTENANCE_SCHEDULE,
    CREATE_COMPONENT_MAINTENANCE_SUBTASK,
    UPDATE_COMPONENT_MAINTENANCE_SUBTASK,
    CREATE_MAINTENANCE_CHECK_SUBTASK,
    UPDATE_COMPONENT_MAINTENANCE_CHECK_SUBTASK,
    CreateMissionTimeline,
    UpdateMissionTimeline,
    CreateEngine_Usage,
    UpdateEngine_Usage,
    CREATE_MAINTENANCE_CATEGORY,
    CREATE_VESSELSTATUS,
    CREATE_R2FILE,
} from '@/app/lib/graphQL/mutation'
import { StylesConfig } from 'react-select'
import FileUpload from '@/components/file-upload'
import { Card, CardContent, CardHeader } from '@/components/ui/card'
import dayjs from 'dayjs'
import { useRouter, useSearchParams, usePathname } from 'next/navigation'
import { useQueryState } from 'nuqs'
import Link from 'next/link'
import { AlertDialogNew } from '@/components/ui/alert-dialog-new'
import { Button } from '@/components/ui/button'
import {
    Pencil,
    Trash,
    Plus,
    Check,
    ArrowLeft,
    CircleX,
    MessageSquareText,
    PencilIcon,
    TrashIcon,
    X,
} from 'lucide-react'
import { Checkbox } from '@/components/ui/checkbox'
import { CheckFieldLabel } from '@/components/ui/check-field-label'
import {
    getVesselList,
    getMaintenanceCheckByID,
    getMaintenanceCheckSubTaskByID,
    isOverDueTask,
} from '@/app/lib/actions'
import { isEmpty } from 'lodash'
import FileItem from '@/components/file-item'
import { useToast } from '@/hooks/use-toast'
// DateField replaced with DatePicker
import { formatDate } from '@/app/helpers/dateHelper'
import { getPermissions, hasPermission } from '@/app/helpers/userHelper'
import Loading from '@/app/loading'
import UploadCloudFlare from '../../logbook/components/upload-cf'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { ActionFooter } from '@/components/ui/action-footer'
import { Combobox } from '@/components/ui/comboBox'
import DatePicker from '@/components/DateRange'
import {
    AlertDialogBody,
    Badge,
    H3,
    H4,
    Label,
    ListHeader,
    P,
    Popover,
    PopoverContent,
    PopoverTrigger,
    Separator,
} from '@/components/ui'

import {
    Tooltip,
    TooltipContent,
    TooltipProvider,
    TooltipTrigger,
} from '@/components/ui/tooltip'
import { Tabs, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { useMediaQuery } from '@reactuses/core'
import { ScrollArea } from '@/components/ui/scroll-area'
import Editor from '../../editor'
import { Progress } from '@/components/ui/progress'
import { DataTable, createColumns } from '@/components/filteredTable'
import { stripHtmlTags } from '@/app/helpers/stringHelper'
import { Avatar, AvatarFallback, getCrewInitials } from '@/components/ui/avatar'
import { useVesselIconData } from '@/app/lib/vessel-icon-helper'
import { ReadSeaLogsMembers } from './query'
import { useBreakpoints } from '@/components/hooks/useBreakpoints'
import {
    getResponsiveLabel,
    useResponsiveLabel,
} from '../../../../../utils/responsiveLabel'
import { cn } from '@/app/lib/utils'
import UploadCloudFlareCaptures, {
    getCloudFlareImagesFile,
} from '../../logbook/components/upload-images'
import CloudFlareCaptures from '../../logbook/components/CloudFlareCaptures'

// Define interfaces for component props and state
interface TaskProps {
    taskId: number
    redirectTo: string
    inSidebar?: boolean
    onSidebarClose?: () => void
    vesselID?: number
}
interface ScheduleTypes {
    value: string
    label: string
}

// Reusable component for record display with edit/delete actions
interface RecordCardProps {
    record: any
    onEdit: (record: any) => void
    onDelete: (recordId: any) => void
    className?: string
}

const RecordCard: React.FC<RecordCardProps> = ({
    record,
    onEdit,
    onDelete,
    className = '',
}) => {
    return (
        <div
            key={`${record.id}-record-${record.time || ''}`}
            className={cn(
                'border border-border bg-card rounded-md relative flex justify-between items-center gap-2 px-2.5',
                className,
            )}>
            <div className="py-2 pe-14 space-y-1">
                {record.description && (
                    <div
                        className=" normal-case line-clamp-2"
                        dangerouslySetInnerHTML={{
                            __html: record.description,
                        }}
                    />
                )}
                {record.author && (
                    <div className="text-[10px] uppercase text-curious-blue-400">
                        <span>
                            {record.author.firstName && (
                                <span>
                                    By {record.author.firstName}{' '}
                                    {record.author.surname}
                                </span>
                            )}
                            {record.time && (
                                <span>
                                    {' • '}
                                    {dayjs(record.time).format(
                                        'MMM DD, YYYY HH:mm',
                                    )}
                                </span>
                            )}
                        </span>
                    </div>
                )}
            </div>
            <div className={`flex absolute top-0 right-0`}>
                <Button
                    iconLeft={PencilIcon}
                    variant="warning"
                    iconOnly
                    size="sm"
                    className="text-sm rounded-none rounded-bl-md p-1 h-fit aspect-square"
                    onClick={() => onEdit(record)}
                />
                <Button
                    iconLeft={X}
                    variant="destructive"
                    iconOnly
                    size="sm"
                    className="h-fit p-1 rounded-none rounded-tr-md aspect-square"
                    onClick={() => onDelete(record.id)}
                />
            </div>
        </div>
    )
}

export default function Task({
    taskId,
    redirectTo,
    inSidebar = false,
    onSidebarClose,
    vesselID = 0,
}: TaskProps) {
    const [isLoading, setIsLoading] = useState(true)
    const router = useRouter()
    const searchParams = useSearchParams()
    const pathname = usePathname()
    const { toast } = useToast()
    const [vessels, setVessels] = useState<any>()
    const [currentVessel, setCurrentVessel] = useState<any>()
    const [crewMembers, setCrewMembers] = useState<any>()
    const [inventories, setInventories] = useState<any>()
    const [maintenanceChecks, setMaintenanceChecks] = useState<any>()
    const [fileLinks, setFileLinks] = useState<any>([])
    const [documents, setDocuments] = useState<Array<Record<string, any>>>([])
    const [currentTask, setCurrentTask] = useState<any>()
    const [expiryDate, setExpiryDate] = useState<any>('')
    const [scheduleCompletedDate, setScheduleCompletedDate] = useState<any>('')
    const [authorID, setAuthorID] = useState<any>(0)
    const [completionDate, setCompletionDate] = useState<any>('')
    const [startDate, setStartDate] = useState<any>(false)
    const [openSubTaskDialog, setOpenSubTaskDialog] = useState(false)
    const [openSubTaskFindingsDialog, setOpenSubTaskFindingsDialog] =
        useState(false)
    const [selectedSubtaskFindings, setSelectedSubtaskFindings] =
        useState<any>(null)
    const [displayRecurringTasks, setDisplayRecurringTasks] = useState(false)
    const [recurringTasks, setRecurringTasks] = useState<any>(false)
    const [subTasks, setSubTask] = useState<any>([])
    const [currentSubTaskCheckID, setCurrentSubTaskCheckID] = useState<any>()
    const [currentSubTask, setCurrentSubTask] = useState<any>()
    const [displayAddFindings, setDisplayAddFindings] = useState(false)
    const [linkSelectedOption, setLinkSelectedOption] = useState<any>([])
    const [displayUpdateSubTask, setDisplayUpdateSubTask] = useState(false)
    const [alertSubTaskStatus, setAlertSubTaskStatus] = useState(false)
    const [displayWarnings, setDisplayWarnings] = useState(false)
    const [costsDifference, setCostsDifference] = useState<any>(0)
    const [openDeleteTaskDialog, setOpenDeleteTaskDialog] = useState(false)
    const [openRecordsDialog, setOpenRecordsDialog] = useState(false)
    const [commentTime, setCommentTime] = useState<any>()
    const [openDeleteRecordDialog, setOpenDeleteRecordDialog] = useState(false)
    const [deleteRecordID, setDeleteRecordID] = useState<any>(0)
    const [commentData, setCommentData] = useState<any>(false)
    const [members, setMembers] = useState<any>()
    const [completedRecurringTasks, setCompletedRecurringTasks] = useState<any>(
        [],
    )
    const [crewInfo, setCrewInfo] = useState<any>()
    const [taskTab, setTaskTab] = useState('task')
    const [inventoryDefaultValue, setInventoryDefaultValue] =
        useState<any>(null)
    const [content, setContent] = useState<any>('')
    const [reviewContent, setReviewContent] = useState<any>('')
    const [subtaskContent, setSubtaskContent] = useState<any>('')
    const [subtaskInventoryValue, setSubtaskInventoryValue] = useState<any>(0)
    const [taskRecords, setTaskRecords] = useState<any[]>([])
    const [engineList, setEngineList] = useState<any>([])
    const [displayCheckEngineCheck, setDisplayCheckEngineCheck] = useState<any>(
        [],
    )
    const [engineHours, setEngineHours] = useState<any>([])
    const [categoryList, setCategoryList] = useState<any>([])
    const [createCategoryDialog, setCreateCategoryDialog] = useState(false)
    // Use nuqs to manage the tab state through URL query parameters
    const [activeTab, setActiveTab] = useQueryState('taskTab', {
        defaultValue: 'Details',
    })
    const [loadedInventory, setLoadedInventory] = useState(0)
    const [currentMaintenanceCheck, setCurrentMaintenanceCheck] =
        useState<any>(false)
    const isDesktop = useMediaQuery('(min-width: 768px)')

    const [permissions, setPermissions] = useState<any>(false)
    const [edit_task, setEdit_task] = useState<any>(false)
    const [delete_task, setDelete_task] = useState<any>(false)
    const [complete_task, setComplete_task] = useState<any>(false)
    const [edit_recurring_task, setEdit_recurring_task] = useState<any>(false)
    const [scheduleEvery, setScheduleEvery] = useState<any>(0)
    const [saveDisabled, setSaveDisabled] = useState(false)
    const [vesselStatus, setVesselStatus] = useState<any>(false)
    const [displayEditStatus, setDisplayEditStatus] = useState(false)
    const [attachments, setAttachments] = useState<any>([])
    const [subTaskAttachments, setSubTaskAttachments] = useState<any>([])
    const [scheduleType, setScheduleType] = useState<ScheduleTypes>({
        value: 'expiry',
        label: 'Due by date',
    })
    const bp = useBreakpoints()
    const [fieldImages, setFieldImages] = useState<any>([])
    const [allImages, setAllImages] = useState<any>([])

    // Use the responsive label hook - this returns a function we can call safely
    const responsiveLabel = useResponsiveLabel()

    // Vessel icon data hook
    const { getVesselWithIcon } = useVesselIconData()
    const taskID = searchParams.get('taskID') ?? 0

    const vesselStatuses: any = [
        { label: 'On Voyage', value: 'OnVoyage' },
        { label: 'Available For Voyage', value: 'AvailableForVoyage' },
        { label: 'Out Of Service', value: 'OutOfService' },
    ]
    const vesselStatusReason: any = [
        { label: 'Crew Unavailable', value: 'CrewUnavailable' },
        { label: 'Skipper/Master Unavailable', value: 'MasterUnavailable' },
        { label: 'Planned Maintenance', value: 'PlannedMaintenance' },
        { label: 'Breakdown', value: 'Breakdown' },
        { label: 'Other', value: 'Other' },
    ]

    const [getVesselStatus] = useLazyQuery(VESSEL_STATUS, {
        fetchPolicy: 'cache-and-network',
        onCompleted: (response) => {
            const data = response.readVesselStatuss.nodes
            if (data) {
                setVesselStatus(data[0])
            }
            if (data.length === 0) {
                createVesselStatus({
                    variables: {
                        input: {
                            date: dayjs().format('YYYY-MM-DD'),
                            vesselID: currentTask?.basicComponentID,
                        },
                    },
                })
            }
        },
        onError: (error) => {
            console.error('Error getting vessel status', error)
        },
    })

    useEffect(() => {
        if (vessels && vessels.length > 0) {
            if (vesselID > 0) {
                const foundVessel = vessels.find(
                    (vessel: any) => +vessel.id === vesselID,
                )
                setCurrentVessel(foundVessel || null)
                foundVessel?.id > 0 &&
                    getVesselStatus({ variables: { id: +vesselID } })
            }
        }
    }, [vessels])

    useEffect(() => {
        if (fieldImages && fieldImages.length > 0) {
            const loadImages = async () => {
                try {
                    const imagePromises = fieldImages.map(async (img: any) => {
                        const imageData = await getCloudFlareImagesFile(img)
                        return {
                            ...img,
                            imageData,
                        }
                    })
                    const images = await Promise.all(imagePromises)
                    setAllImages(images)
                } catch (error) {
                    console.error('Error loading images:', error)
                }
            }
            loadImages()
        } else {
            setAllImages([])
        }
    }, [fieldImages])

    const init_permissions = () => {
        if (permissions) {
            if (hasPermission('EDIT_TASK', permissions)) {
                setEdit_task(true)
            } else {
                setEdit_task(false)
            }
            if (hasPermission('DELETE_TASK', permissions)) {
                setDelete_task(true)
            } else {
                setDelete_task(false)
            }
            if (hasPermission('COMPLETE_TASK', permissions)) {
                setEdit_recurring_task(true)
                setComplete_task(true)
            }
            if (hasPermission('EDIT_RECURRING_TASK', permissions)) {
                setEdit_recurring_task(true)
                setComplete_task(true)
            }
        }
    }

    useEffect(() => {
        setPermissions(getPermissions)
        init_permissions()
        getFieldImages({
            variables: {
                filter: {
                    componentMaintenanceCheckID: { eq: taskID },
                },
            },
        })
    }, [])

    const refreshImages = async () => {
        await getFieldImages({
            variables: {
                filter: {
                    componentMaintenanceCheckID: { eq: taskID },
                },
            },
        })
    }

    useEffect(() => {
        init_permissions()
    }, [permissions])

    useEffect(() => {
        const vesselId =
            maintenanceChecks?.basicComponentID > 0
                ? maintenanceChecks.basicComponentID
                : maintenanceChecks?.inventory?.vessel?.id
        if (+vesselId !== +vesselStatus?.id) {
            vesselId > 0 && getVesselStatus({ variables: { id: +vesselId } })
        }

        if (vesselID === 0 && +vesselId > 0 && vessels) {
            setCurrentVessel(
                vessels.find((vessel: any) => +vessel.id === +vesselId),
            )
        }
    }, [vessels, maintenanceChecks])

    const handleSetMemberList = (members: any) => {
        setMembers(
            members
                ?.filter(
                    (member: any) =>
                        member.archived == false && member.firstName != '',
                )
                ?.map((member: any) => ({
                    label: member.firstName + ' ' + member.surname,
                    value: member.id,
                    crew: member,
                    profile: {
                        firstName: member.firstName,
                        surname: member.surname,
                        avatar: null,
                    },
                })),
        )
    }

    const handleCommentTimeChange = (date: any) => {
        setCommentTime(date)
    }

    const handleScheduleCompletedDateChange = (newValue: any) => {
        setScheduleCompletedDate(newValue)
    }

    const handleEditorChange = (value: any) => {
        setContent(value)
    }

    const handleReviewEditorChange = (value: any) => {
        setReviewContent(value)
    }

    const handleSubtaskEditorChange = (value: any) => {
        setSubtaskContent(value)
    }

    const handleSetSubTask = (data: any) => {
        setSubTask(data)
    }

    getMaintenanceCheckSubTaskByID(taskId, handleSetSubTask)

    const [querySeaLogsMembersList] = useLazyQuery(ReadSeaLogsMembers, {
        fetchPolicy: 'cache-and-network',
        onError: (error: any) => {
            console.error('querySeaLogsMembersList error', error)
        },
    })

    const loadCrewMembers = async () => {
        let allMembers: any[] = []
        let offset = 0
        const limit = 100
        let hasNextPage = true

        try {
            while (hasNextPage) {
                const response = await querySeaLogsMembersList({
                    variables: {
                        filter: { isArchived: { eq: false } },
                        limit: limit,
                        offset: offset,
                    },
                })

                if (response.data?.readSeaLogsMembers) {
                    const data = response.data.readSeaLogsMembers.nodes
                    const pageInfo = response.data.readSeaLogsMembers.pageInfo

                    if (data && data.length > 0) {
                        allMembers = [...allMembers, ...data]
                    }

                    hasNextPage = pageInfo?.hasNextPage || false
                    offset += limit
                } else {
                    hasNextPage = false
                }
            }

            // Set all collected members at once
            if (allMembers.length > 0) {
                setCrewMembers(allMembers)
                handleSetMemberList(allMembers)
            }
        } catch (error) {
            console.error('Error loading all crew members:', error)
        }
    }
    useEffect(() => {
        if (isLoading) {
            loadCrewMembers()
            setIsLoading(false)
        }
    }, [isLoading])

    const [queryInventories] = useLazyQuery(GET_INVENTORY_LIST, {
        fetchPolicy: 'cache-and-network',
        onCompleted: (response: any) => {
            const data = response.readInventoryList[0].list
            if (data) {
                setInventories(data)
            }
        },
        onError: (error: any) => {
            console.error('queryInventoriesEntry error', error)
        },
    })

    const [queryInventoriesByVessel] = useLazyQuery(GET_INVENTORY_LIST, {
        fetchPolicy: 'cache-and-network',
        onCompleted: (response: any) => {
            const data = response.readInventoryList[0].list
            if (data) {
                setInventories(
                    data.filter(
                        (inventory: any) =>
                            inventory.vessel.id == loadedInventory,
                    ),
                )
                setInventories(
                    data.filter(
                        (inventory: any) =>
                            inventory.vessel.id == loadedInventory,
                    ),
                )
            }
        },
        onError: (error: any) => {
            console.error('queryInventoriesEntry error', error)
        },
    })

    const loadInventories = async () => {
        const vesselID =
            currentTask?.basicComponentID > 0
                ? currentTask.basicComponentID
                : maintenanceChecks?.basicComponentID
        if (vesselID > 0) {
            if (loadedInventory != vesselID) {
                setLoadedInventory(vesselID)
                queryInventoriesByVessel({
                    variables: {
                        vesselID: +vesselID,
                    },
                })
            }
        } else {
            setLoadedInventory(0)
            await queryInventories({
                variables: {
                    vesselID: 0,
                },
            })
        }
    }

    useEffect(() => {
        loadInventories()
    }, [maintenanceChecks, currentTask])

    const handleVesselList = (vessels: any) => {
        const activeVessels = vessels.filter((vessel: any) => !vessel.archived)
        const appendedData = activeVessels.map((item: any) => ({
            ...item,
        }))
        appendedData.push({ title: 'Other', id: 0 })
        setVessels(appendedData)
    }

    getVesselList(handleVesselList)

    const handleSetMaintenanceChecks = (data: any) => {
        setCurrentMaintenanceCheck(data)
        data?.startDate && setStartDate(dayjs(data.startDate))
        {
            data?.expires &&
                data?.maintenanceScheduleID == 0 &&
                setExpiryDate(dayjs(data.expires))
        }
        {
            data?.dateCompleted
                ? setCompletionDate(dayjs(data.dateCompleted))
                : data?.completed && setCompletionDate(dayjs(data.completed))
        }
        if (data?.maintenanceScheduleID > 0) {
            setDisplayRecurringTasks(true)
            setScheduleEvery(data?.maintenanceSchedule?.occursEvery)
            setRecurringTasks(data?.maintenanceSchedule)
            upcomingScheduleDate(data)
            data?.basicComponentID > 0 &&
                getEngineIdsByVessel({
                    variables: {
                        id: +data?.basicComponentID,
                    },
                })
        }

        setMaintenanceChecks(data)
        setScheduleType({
            value: data?.taskType ?? 'expiry',
            label:
                data?.taskType == 'Expiry'
                    ? 'Due by date'
                    : data?.taskType == 'EngineHours'
                      ? 'Due by engine hours'
                      : data?.taskType == 'Uses'
                        ? 'Due by number of uses'
                        : data?.taskType == 'Frequency'
                          ? 'Recurring task'
                          : 'Due by date',
        })
        setInventoryDefaultValue(
            data?.inventory?.id > 0
                ? {
                      label: data.inventory.item,
                      value: data.inventory.id,
                  }
                : {
                      label: null,
                      value: '0',
                  },
        )
        {
            data?.documents.nodes?.length > 0 &&
                getFiles(
                    data?.documents.nodes
                        ?.map((link: any) => link.id)
                        .join(','),
                )
        }
        {
            data?.attachmentLinks?.nodes &&
                setLinkSelectedOption(
                    data?.attachmentLinks?.nodes.map((link: any) => ({
                        label: link.link,
                        value: link.id,
                    })),
                )
        }
        setCurrentTask({
            ...currentTask,
            status: data?.status.replaceAll('_', ' '),
        })
        const difference =
            parseInt(data?.projected ?? '0') - parseInt(data?.actual ?? '0')
        setCostsDifference(difference)
        data?.documents?.nodes?.length > 0 &&
            setDocuments(data?.documents?.nodes)
        data?.comments ? setContent(data?.comments) : setContent('')
        data?.R2File?.nodes?.length > 0 && setAttachments(data?.R2File?.nodes)
    }

    const updateCostsDifference = (e: any) => {
        const projectedElement = document.getElementById(
            'task-projected',
        ) as HTMLInputElement
        const actualElement = document.getElementById(
            'task-actual',
        ) as HTMLInputElement

        const projected = projectedElement.value ? +projectedElement.value : 0
        const actual = actualElement.value ? +actualElement.value : 0

        setCostsDifference(projected - actual)
    }

    getMaintenanceCheckByID(taskId, handleSetMaintenanceChecks)

    const getFiles = async (ids: string) => {
        await queryFiles({
            variables: {
                id: ids,
            },
        })
    }

    const [queryFiles] = useLazyQuery(GET_FILES, {
        fetchPolicy: 'cache-and-network',
        onCompleted: (response: any) => {
            const { isSuccess, data } = response.getFile
            if (isSuccess) {
                documents.push(data)
                setDocuments(documents)
            }
        },
        onError: (error: any) => {
            console.error('queryFilesEntry error', error)
        },
    })

    const [createSeaLogsFileLinks] = useMutation(CREATE_SEALOGS_FILE_LINKS, {
        onCompleted: (response: any) => {
            const data = response.createSeaLogsFileLinks
            if (data.id > 0) {
                const newLinks = [...fileLinks, data]
                setFileLinks(newLinks)
                linkSelectedOption
                    ? setLinkSelectedOption([
                          ...linkSelectedOption,
                          { label: data.link, value: data.id },
                      ])
                    : setLinkSelectedOption([
                          { label: data.link, value: data.id },
                      ])
            }
        },
        onError: (error: any) => {
            console.error('createSeaLogsFileLinksEntry error', error)
        },
    })

    const priorityOptions = [
        { value: 'None', label: 'None', color: '#1f2937' },
        { value: 'Low', label: 'Low', color: '#15803d' },
        { value: 'Medium', label: 'Medium', color: '#f97316' },
        { value: 'High', label: 'High', color: '#e11d48' },
    ]

    const recurringType = [
        { label: 'Engine Hours', value: 'Hours' },
        { label: 'Days', value: 'Days' },
        { label: 'Weeks', value: 'Weeks' },
        { label: 'Months', value: 'Months' },
        { label: 'Number of sailings', value: 'Uses' },
    ]

    const colourStyles: StylesConfig = {
        option: (
            styles: any,
            {
                data,
                isDisabled,
                isFocused,
                isSelected,
            }: { data: any; isDisabled: any; isFocused: any; isSelected: any },
        ) => {
            const color = data.color
            return {
                ...styles,
                backgroundColor: isDisabled
                    ? undefined
                    : isSelected
                      ? data.color + '20'
                      : isFocused
                        ? data.color + '20'
                        : undefined,
                color: data.color,
            }
        },
        singleValue: (styles: any, data: any) => ({
            ...styles,
            color: priorityOptions.find(
                (option: any) => option.value == data.data.value,
            )?.color,
        }),
    }

    const statusOptions = [
        { value: 'Open', label: 'Open' },
        { value: 'Save As Draft', label: 'Save as Draft' },
        { value: 'In Progress', label: 'In Progress' },
        { value: 'On Hold', label: 'On Hold' },
        { value: 'Completed', label: 'Completed' },
    ]

    const subtaskProgress = Number(
        (subTasks.filter((subtask: any) => subtask.status == 'Completed')
            .length /
            subTasks.length) *
            100,
    )

    // Wrapper function for ActionFooter
    const handleUpdateTask = () => {
        handleUpdate({})
    }

    const handleUpdate = async (e: any) => {
        if (!edit_task) {
            toast({
                title: 'Error',
                description: 'You do not have permission to edit this task',
                variant: 'destructive',
            })
            return
        }
        if (displayRecurringTasks && !edit_recurring_task) {
            toast({
                title: 'Error',
                description:
                    'You do not have permission to edit this recurring task',
                variant: 'destructive',
            })
            return
        }
        if (displayRecurringTasks && typeof e === 'object') {
            const titleElement = document.getElementById(
                'task-title',
            ) as HTMLInputElement
            const title = titleElement ? titleElement.value : '' // Add null checks

            const frequency =
                scheduleEvery > 0 ? scheduleEvery : currentTask.occursEvery

            if (isNaN(+frequency) || +frequency <= 0) {
                setActiveTab('Recurring schedule')
                toast({
                    title: 'Error',
                    description:
                        'You need to set the schedule of this recurring task. Please go to the "Recurring schedule" tab and set the frequency.',
                    variant: 'destructive',
                })
                return
            }
            const type = currentTask.occursEveryType
            const scheduleType =
                currentTask.occursEveryType === 'Hours'
                    ? 'DutyHours'
                    : currentTask.occursEveryType === 'Uses'
                      ? 'EquipmentUsages'
                      : 'Frequency'

            const descriptionElement = document.getElementById(
                'recurring-task-description',
            ) as HTMLInputElement
            const description = descriptionElement
                ? descriptionElement.value
                : ''

            const highWarnWithinElement = document.getElementById(
                'high-warn-within',
            ) as HTMLInputElement
            const highWarnWithin = highWarnWithinElement
                ? highWarnWithinElement.value
                : ''

            const mediumWarnWithinElement = document.getElementById(
                'medium-warn-within',
            ) as HTMLInputElement
            const mediumWarnWithin = mediumWarnWithinElement
                ? mediumWarnWithinElement.value
                : ''

            const lowWarnWithinElement = document.getElementById(
                'low-warn-within',
            ) as HTMLInputElement
            const lowWarnWithin = lowWarnWithinElement
                ? lowWarnWithinElement.value
                : ''

            var schedule: any = {
                title: title,
                description: description,
                type: scheduleType,
                occursEveryType: type,
                highWarnWithin: +highWarnWithin,
                mediumWarnWithin: +mediumWarnWithin,
                lowWarnWithin: +lowWarnWithin,
                warnWithinType: type,
                maintenanceChecks: maintenanceChecks.id,
                basicComponents: currentTask.basicComponentID
                    ? currentTask.basicComponentID
                    : maintenanceChecks.basicComponentID,
                inventoryID:
                    currentTask.InventoryID >= 0
                        ? currentTask.InventoryID
                        : maintenanceChecks.inventoryID,
            }

            if (recurringTasks?.id > 0) {
                schedule.id = recurringTasks.id
            }

            if (frequency) {
                schedule.occursEvery = +frequency
            }

            {
                recurringTasks?.id > 0
                    ? await updateMaintenanceSchedule({
                          variables: {
                              input: schedule,
                          },
                      })
                    : await createMaintenanceSchedule({
                          variables: {
                              input: schedule,
                          },
                      })
            }
        } else {
            const nameElement = document.getElementById(
                'task-name',
            ) as HTMLInputElement
            const name = nameElement ? nameElement.value : false
            if (!name) {
                toast({
                    title: 'Error',
                    description: 'You need to set the name of this task',
                    variant: 'destructive',
                })
                return
            }

            const workorderElement = document.getElementById(
                'task-workorder',
            ) as HTMLInputElement
            const workorder = workorderElement ? workorderElement.value : ''

            const description = content

            const projectedElement = document.getElementById(
                'task-projected',
            ) as HTMLInputElement
            const projected = projectedElement ? projectedElement.value : ''

            const actualElement = document.getElementById(
                'task-actual',
            ) as HTMLInputElement
            const actual = actualElement ? actualElement.value : ''

            const differenceElement = document.getElementById(
                'task-difference',
            ) as HTMLInputElement
            const difference = differenceElement ? differenceElement.value : ''
            await updateMaintenanceChecks({
                variables: {
                    input: {
                        id: maintenanceChecks.id,
                        workOrderNumber: workorder,
                        projected: projected
                            ? +projected
                            : +maintenanceChecks.projected,
                        actual: actual ? +actual : +maintenanceChecks.actual,
                        difference: difference
                            ? +difference
                            : +maintenanceChecks.difference,
                        name: name,
                        startDate: startDate
                            ? dayjs(startDate).format('YYYY-MM-DD')
                            : maintenanceChecks.startDate,
                        completed: expiryDate
                            ? expiryDate === 'Invalid Date'
                                ? maintenanceChecks.completed
                                : expiryDate
                            : maintenanceChecks.completed,
                        completedByID: currentTask.completedBy,
                        dateCompleted: completionDate
                            ? completionDate
                            : maintenanceChecks.dateCompleted,
                        expires:
                            expiryDate === 'Invalid Date' ? null : expiryDate,
                        comments: description,
                        severity: currentTask.severity,
                        maintenanceCategoryID: currentTask.category,
                        status: currentTask.status
                            ? currentTask.status
                            : maintenanceChecks.status.replaceAll('_', ' '),
                        documents:
                            documents.length > 0
                                ? documents
                                      ?.map((doc: any) => +doc.id)
                                      .join(',')
                                : maintenanceChecks.documents?.nodes
                                      .map((doc: any) => +doc.id)
                                      .join(','),
                        attachmentLinks: linkSelectedOption
                            ? linkSelectedOption
                                  .map((link: any) => link.value)
                                  .join(',')
                            : maintenanceChecks.attachmentLinks?.nodes
                                  .map((link: any) => link.id)
                                  .join(','),
                        assignees: currentTask.assignees
                            ? currentTask.assignees
                            : maintenanceChecks.assignees?.nodes
                                  .map((assignee: any) => assignee.id)
                                  .join(','),
                        assignedToID: currentTask.assignees
                            ? +currentTask.assignees
                            : maintenanceChecks?.assignedToID,
                        basicComponentID: currentTask.basicComponentID
                            ? currentTask.basicComponentID
                            : maintenanceChecks.basicComponentID,
                        inventoryID:
                            currentTask.inventoryID >= 0
                                ? currentTask.inventoryID
                                : maintenanceChecks.inventoryID,
                        maintenanceCheck_SignatureID:
                            maintenanceChecks.maintenanceCheck_SignatureID,
                        recurringID:
                            +maintenanceChecks.recurringID === 0 &&
                            recurringTasks
                                ? taskId
                                : maintenanceChecks.recurringID,
                        startHours:
                            currentMaintenanceCheck?.startHours >= 0
                                ? +currentMaintenanceCheck.startHours
                                : +maintenanceChecks.startHours,
                        hoursCompleted:
                            currentMaintenanceCheck.hoursCompleted >= 0
                                ? +currentMaintenanceCheck.hoursCompleted
                                : +maintenanceChecks.hoursCompleted,
                        taskType: scheduleType.value,
                    },
                },
            })
        }
    }

    const [createMaintenanceSchedule] = useMutation(
        CREATE_COMPONENT_MAINTENANCE_SCHEDULE,
        {
            onCompleted: (response: any) => {
                const data = response.createComponentMaintenanceSchedule
                if (data.id > 0) {
                    handleUpdate(true)
                }
            },
            onError: (error: any) => {
                handleUpdate(true)
                console.error('createMaintenanceScheduleEntry error', error)
            },
        },
    )

    const [createBlankMaintenanceSchedule] = useMutation(
        CREATE_COMPONENT_MAINTENANCE_SCHEDULE,
        {
            onCompleted: (response: any) => {
                const data = response.createComponentMaintenanceSchedule
                updateWithoutCreateMaintenanceChecks({
                    variables: {
                        input: {
                            id: taskId,
                            maintenanceScheduleID: data.id,
                        },
                    },
                })
                setRecurringTasks(data)
            },
            onError: (error: any) => {
                console.error('createMaintenanceScheduleEntry error', error)
            },
        },
    )

    const [updateWithoutCreateMaintenanceChecks] = useMutation(
        UPDATE_COMPONENT_MAINTENANCE_CHECK,
        {
            onCompleted: (response: any) => {
                const data = response.updateComponentMaintenanceCheck
            },
            onError: (error: any) => {
                console.error('updateMaintenanceChecksEntry error', error)
            },
        },
    )

    const [updateMaintenanceSchedule] = useMutation(
        UPDATE_COMPONENT_MAINTENANCE_SCHEDULE,
        {
            onCompleted: (response: any) => {
                const data = response.updateComponentMaintenanceSchedule
                if (data.id > 0) {
                    handleUpdate(true)
                }
            },
            onError: (error: any) => {
                handleUpdate(true)
                console.error('updateMaintenanceScheduleEntry error', error)
            },
        },
    )

    const [updateMaintenanceChecks] = useMutation(
        UPDATE_COMPONENT_MAINTENANCE_CHECK,
        {
            onCompleted: (response: any) => {
                const data = response.updateComponentMaintenanceCheck
                if (
                    displayRecurringTasks &&
                    maintenanceChecks.status != 'Completed' &&
                    currentTask.status == 'Completed'
                ) {
                    router.push(
                        '/maintenance/complete-recurring-task?taskID=' + taskId,
                    )
                } else if (data.id > 0) {
                    if (
                        searchParams.get('taskCreated') ||
                        searchParams.get('taskCompleted')
                    ) {
                        if (redirectTo === 'inventory') {
                            router.push(
                                `/inventory/view?id=${
                                    currentTask.inventoryID
                                        ? currentTask.inventoryID
                                        : maintenanceChecks.inventoryID
                                }&inventoryTab=maintenance`,
                            )
                        } else {
                            searchParams.get('redirect_to')
                                ? router.push(
                                      searchParams?.get('redirect_to') + '',
                                  )
                                : router.push('/maintenance')
                        }
                    } else {
                        if (!inSidebar) {
                            searchParams.get('redirect_to')
                                ? router.push(
                                      searchParams?.get('redirect_to') + '',
                                  )
                                : router.push('/maintenance')
                        } else {
                            onSidebarClose && onSidebarClose()
                        }
                    }
                }
            },
            onError: (error: any) => {
                console.error('updateMaintenanceChecksEntry error', error)
            },
        },
    )

    const handleCancel = () => {
        if (!inSidebar) {
            if (
                searchParams.get('taskCreated') ||
                searchParams.get('taskCompleted')
            ) {
                searchParams.get('redirect_to')
                    ? router.push(searchParams?.get('redirect_to') + '')
                    : router.push('/maintenance')
            } else {
                router.back()
            }
        } else {
            onSidebarClose && onSidebarClose()
        }
    }

    const handleExpiryChange = (newValue: any) => {
        setExpiryDate(newValue)
    }

    const handleCompletionChange = (newValue: any) => {
        setCompletionDate(newValue)
    }

    const handleStartDateChange = (newValue: any) => {
        setStartDate(dayjs(newValue))
        updateDueDate(newValue)
    }

    const handleDeleteCheck = async () => {
        if (!delete_task) {
            onSidebarClose && onSidebarClose()
            toast({
                title: 'Error',
                description: 'You do not have permission to delete this task',
                variant: 'destructive',
            })
            return
        }
        if (displayRecurringTasks && !edit_recurring_task) {
            toast({
                title: 'Error',
                description: 'You do not have permission to delete this task',
                variant: 'destructive',
            })
            return
        }
        await deleteMaintenanceCheck({
            variables: {
                id: [+taskId],
            },
        })
    }

    const [deleteMaintenanceCheck] = useMutation(
        DELETE_COMPONENT_MAINTENANCE_CHECK,
        {
            onCompleted: (response: any) => {
                if (!inSidebar) {
                    router.back()
                } else {
                    onSidebarClose && onSidebarClose()
                }
            },
            onError: (error: any) => {
                console.error('deleteMaintenanceCheckEntry error', error)
            },
        },
    )

    const handleCreateSubTask = () => {
        const subTaskName = (
            document.getElementById('subtask-name') as HTMLInputElement
        ).value
        setOpenSubTaskDialog(false)
        createSubtask({
            variables: {
                input: {
                    task: subTaskName,
                    description: subtaskContent,
                    inventoryID: subtaskInventoryValue,
                },
            },
        })
    }

    const [createSubtask] = useMutation(CREATE_COMPONENT_MAINTENANCE_SUBTASK, {
        onCompleted: (response: any) => {
            const data = response.createMaintenanceScheduleSubTask
            if (data) {
                createSubtaskCheck({
                    variables: {
                        input: {
                            maintenanceScheduleSubTaskID: data.id,
                            componentMaintenanceCheckID: taskId,
                            status: 'In Review',
                        },
                    },
                })
            }
        },
        onError: (error: any) => {
            console.error('createSubtaskEntry error', error)
        },
    })

    const [queryMaintenanceCheckSubTask] = useLazyQuery(
        GET_MAINTENANCE_CHECK_SUBTASK,
        {
            fetchPolicy: 'cache-and-network',
            onCompleted: (response: any) => {
                const data = response.readMaintenanceCheckSubTasks.nodes
                if (data) {
                    setSubTask(data)
                }
            },
            onError: (error: any) => {
                console.error('queryMaintenanceCheckSubTask error', error)
            },
        },
    )

    const loadMaintenanceCheckSubTask = async () => {
        await queryMaintenanceCheckSubTask({
            variables: {
                id: +taskId,
            },
        })
    }

    const [createSubtaskCheck] = useMutation(CREATE_MAINTENANCE_CHECK_SUBTASK, {
        onCompleted: (response: any) => {
            const data = response.createMaintenanceCheckSubTask
            if (+data.id > 0) {
                loadMaintenanceCheckSubTask()
            }
        },
        onError: (error: any) => {
            console.error('createSubtaskCheckEntry error', error)
        },
    })

    useEffect(() => {
        if (attachments.length > 0) {
            attachments.map((receipt: any) => {
                if (!receipt.id && taskId > 0) {
                    createAttachments({
                        variables: {
                            input: {
                                title: receipt.title,
                                componentMaintenanceCheckID: taskId,
                            },
                        },
                    })
                }
            })
        }
    }, [attachments])

    const [createAttachments] = useMutation(CREATE_R2FILE, {
        onCompleted: (response) => {
            const data = response.createR2File
            const newReceipts = attachments.map((receipt: any) => {
                if (receipt.title === data.title) {
                    return {
                        ...receipt,
                        id: data.id,
                    }
                }
                return receipt
            })
            setAttachments(newReceipts)
        },
        onError: (error) => {
            console.error('Error creating fuel receipts', error)
        },
    })

    useEffect(() => {
        if (subTaskAttachments.length > 0) {
            subTaskAttachments.map((receipt: any) => {
                if (!receipt.id && currentSubTaskCheckID > 0) {
                    createSubTaskAttachments({
                        variables: {
                            input: {
                                title: receipt.title,
                                maintenanceCheckSubTaskID:
                                    currentSubTaskCheckID,
                            },
                        },
                    })
                }
            })
        }
    }, [subTaskAttachments])

    const [createSubTaskAttachments] = useMutation(CREATE_R2FILE, {
        onCompleted: (response) => {
            const data = response.createR2File
            const newReceipts = attachments.map((receipt: any) => {
                if (receipt.title === data.title) {
                    return {
                        ...receipt,
                        id: data.id,
                    }
                }
                return receipt
            })
            setSubTaskAttachments(newReceipts)
        },
        onError: (error) => {
            console.error('Error creating fuel receipts', error)
        },
    })

    const handleDisplayRecurringTasks = (e: any) => {
        if (!edit_recurring_task) {
            toast({
                title: 'Error',
                description:
                    'You do not have permission to change recurring tasks',
                variant: 'destructive',
            })
            return
        }
        setDisplayRecurringTasks(e.target.checked)
        if (!recurringTasks?.id) {
            createBlankMaintenanceSchedule({
                variables: {
                    input: {},
                },
            })
        }
        if (e.target.checked === false && recurringTasks?.id) {
            updateWithoutCreateMaintenanceChecks({
                variables: {
                    input: {
                        id: taskId,
                        maintenanceScheduleID: 0,
                    },
                },
            })
        }
    }

    const updateDueDate = (newStartDate: any = false) => {
        const occursEveryType =
            currentMaintenanceCheck.maintenanceSchedule.occursEveryType
        if (occursEveryType === 'Hours' || occursEveryType === 'Uses') {
            if (occursEveryType === 'Uses') {
                // setExpiryDate(dayjs(maintenanceChecks.equipmentUsagesAtCheck).format('DD/MM/YYYY'))
            }
            // setExpiryDate(dayjs(maintenanceChecks.dutyHoursAtCheck).format('DD/MM/YYYY'))
        } else {
            const occursEvery = scheduleEvery
            const lastCompletedDate = dayjs(
                startDate &&
                    new Date(dayjs(startDate).toISOString()).getTime() > 0
                    ? new Date(newStartDate ? newStartDate : startDate)
                    : new Date(),
            ).startOf('day')
            const nextOccurrence = lastCompletedDate.add(
                occursEvery,
                occursEveryType?.slice(0, -1).toLowerCase(),
            )
            if (nextOccurrence.format('DD/MM/YYYY') != 'Invalid Date') {
                setExpiryDate(nextOccurrence)
            }
        }
    }

    const upcomingScheduleDate = (maintenanceChecks: any) => {
        setCurrentTask({
            ...currentTask,
            occursEveryType:
                maintenanceChecks?.maintenanceSchedule?.occursEveryType,
        })
        const recurringTasks = maintenanceChecks?.maintenanceSchedule
        if (maintenanceChecks?.maintenanceSchedule?.id > 0) {
            const occursEveryType = recurringTasks.occursEveryType
                ? recurringTasks.occursEveryType
                : 'Days'
            if (occursEveryType === 'Hours' || occursEveryType === 'Uses') {
                if (occursEveryType === 'Uses') {
                    return (
                        maintenanceChecks.equipmentUsagesAtCheck +
                        ' Equipment Uses'
                    )
                }
            } else {
                const occursEvery = recurringTasks.occursEvery
                    ? recurringTasks.occursEvery
                    : 1
                const lastCompletedDate = dayjs(
                    maintenanceChecks?.startDate
                        ? new Date(maintenanceChecks.startDate)
                        : new Date(),
                ).startOf('day')
                const nextOccurrence = lastCompletedDate.add(
                    occursEvery,
                    occursEveryType,
                )
                setExpiryDate(nextOccurrence)
                return nextOccurrence.format('DD/MM/YYYY')
            }
        }
    }

    const lastScheduleDate = () => {
        if (recurringTasks) {
            const occursEveryType = recurringTasks.occursEveryType
                ? recurringTasks.occursEveryType
                : 'Days'
            if (occursEveryType !== 'Hours' && occursEveryType !== 'Uses') {
                const lastCompleted = maintenanceChecks?.startDate
                    ? dayjs(maintenanceChecks.startDate)
                    : dayjs()
                return lastCompleted
            }
        }
        return dayjs()
    }

    const handleUpdateSubTask = (e: any) => {
        if (e?.target?.checked) {
            setCurrentSubTaskCheckID(e.target.id)
            setCurrentSubTask(
                subTasks.filter((subtask: any) => subtask.id == e.target.id)[0]
                    .maintenanceScheduleSubTask.id,
            )
            setSubtaskContent(
                subTasks.filter((subtask: any) => subtask.id == e.target.id)[0]
                    .maintenanceScheduleSubTask.description,
            )
            setSubtaskInventoryValue(
                subTasks.filter((subtask: any) => subtask.id == e.target.id)[0]
                    .maintenanceScheduleSubTask.inventoryID,
            )
            subTasks.find((subtask: any) => subtask.id == e.target.id)?.R2File
                ?.nodes?.length > 0
                ? setSubTaskAttachments(
                      subTasks.find((subtask: any) => subtask.id == e.target.id)
                          .R2File.nodes,
                  )
                : setSubTaskAttachments([])
            setDisplayUpdateSubTask(true)
            setAlertSubTaskStatus(false)
        }
        if (e?.target?.checked === false) {
            setAlertSubTaskStatus(true)
            setCurrentSubTask(
                subTasks.filter((subtask: any) => subtask.id == e.target.id)[0]
                    .maintenanceScheduleSubTask.id,
            )
            setCurrentSubTaskCheckID(e.target.id)
            setSubtaskContent(
                subTasks.filter((subtask: any) => subtask.id == e.target.id)[0]
                    .maintenanceScheduleSubTask.description,
            )
            setSubtaskInventoryValue(
                subTasks.find((subtask: any) => subtask.id == e.target.id)
                    .maintenanceScheduleSubTask.inventoryID,
            )
            subTasks.find((subtask: any) => subtask.id == e.target.id)?.R2File
                ?.nodes?.length > 0
                ? setSubTaskAttachments(
                      subTasks.find((subtask: any) => subtask.id == e.target.id)
                          .R2File.nodes,
                  )
                : setSubTaskAttachments([])
            setDisplayUpdateSubTask(true)
        }
        if (e === 'updateFindings') {
            const subtaskFindings = (
                document.getElementById('subtask-findings') as HTMLInputElement
            ).value
            setDisplayAddFindings(false)
            setAuthorID(0)
            setScheduleCompletedDate('')
            updateSubtaskCheck({
                variables: {
                    input: {
                        id: currentSubTaskCheckID,
                        findings: subtaskFindings,
                        completedByID: authorID,
                        dateCompleted: scheduleCompletedDate,
                    },
                },
            })
        }
        if (e === 'updateSubTask') {
            const subTaskName = (
                document.getElementById('subtask-name') as HTMLInputElement
            ).value
            setDisplayUpdateSubTask(false)
            const dateCompleted = subTasks.find(
                (subtask: any) => subtask.id === currentSubTaskCheckID,
            )?.dateCompleted
                ? dayjs(
                      subTasks.find(
                          (subtask: any) =>
                              subtask.id === currentSubTaskCheckID,
                      ).dateCompleted,
                  )
                : ''
            alertSubTaskStatus
                ? (setDisplayAddFindings(true),
                  setAuthorID(
                      subTasks.find(
                          (subtask: any) =>
                              subtask.id === currentSubTaskCheckID,
                      )?.completedBy?.id,
                  ),
                  setScheduleCompletedDate(dateCompleted))
                : setDisplayAddFindings(false)
            updateSubtask({
                variables: {
                    input: {
                        id: currentSubTask,
                        task: subTaskName,
                        description: subtaskContent,
                        inventoryID: subtaskInventoryValue,
                    },
                },
            })
            updateSubtaskCheck({
                variables: {
                    input: {
                        id: currentSubTaskCheckID,
                        status: alertSubTaskStatus ? 'Completed' : 'In Review',
                    },
                },
            })
        }
        if (e === 'deleteSubTask') {
            const subtaskFindings = subTasks.filter(
                (subtask: any) => subtask.id == currentSubTaskCheckID,
            )[0].findings
            updateSubtaskCheck({
                variables: {
                    input: {
                        id: currentSubTaskCheckID,
                        componentMaintenanceCheckID: '0',
                        findings:
                            subtaskFindings +
                            'deleted from task ' +
                            currentSubTaskCheckID,
                    },
                },
            })
            setDisplayUpdateSubTask(false)
        }
    }

    const [updateSubtask] = useMutation(UPDATE_COMPONENT_MAINTENANCE_SUBTASK, {
        onCompleted: (response: any) => {
            const data = response.updateMaintenanceScheduleSubTask
            if (data.id > 0) {
                loadMaintenanceCheckSubTask()
            }
        },
        onError: (error: any) => {
            console.error('updateSubtaskEntry error', error)
        },
    })

    const [updateSubtaskCheck] = useMutation(
        UPDATE_COMPONENT_MAINTENANCE_CHECK_SUBTASK,
        {
            onCompleted: (response: any) => {
                const data = response.updateMaintenanceCheckSubTask
                if (data.id > 0) {
                    loadMaintenanceCheckSubTask()
                }
            },
            onError: (error: any) => {
                console.error('updateSubtaskCheckEntry error', error)
            },
        },
    )

    const handleDisplayWarnings = (checked: boolean) => {
        setDisplayWarnings(checked)
    }

    const deleteFile = async (id: number) => {
        const newDocuments = documents.filter((doc: any) => doc.id !== id)
        setDocuments(newDocuments)
    }

    const [queryCrewMemberInfo] = useLazyQuery(GET_CREW_BY_IDS, {
        fetchPolicy: 'cache-and-network',
        onCompleted: (response: any) => {
            const data = response.readSeaLogsMembers.nodes
            if (data) {
                setCrewInfo(data)
            }
        },
        onError: (error) => {
            console.error('queryCrewMemberInfo error', error)
        },
    })
    const loadCrewMemberInfo = async (crewId: any) => {
        await queryCrewMemberInfo({
            variables: {
                crewMemberIDs: crewId.length > 0 ? crewId : [0],
            },
        })
    }
    const [readComponentMaintenanceChecks] = useLazyQuery(
        GET_MAINTENANCE_CHECK,
        {
            fetchPolicy: 'no-cache',
            onCompleted: (response: any) => {
                const data = response.readComponentMaintenanceChecks
                if (data.nodes.length > 0) {
                    const crewIds: number[] = Array.from(
                        new Set(
                            data.nodes
                                .filter((item: any) => item.assignedToID > 0)
                                .map((item: any) => item.assignedToID),
                        ),
                    )
                    loadCrewMemberInfo(crewIds)
                    var maintenanceChecksArray = data.nodes.map(
                        (maintenanceCheck: any) => {
                            return {
                                id: maintenanceCheck.id,
                                name: maintenanceCheck.name,
                                basicComponentID:
                                    maintenanceCheck.basicComponentID,
                                comments: maintenanceCheck.comments,
                                description: maintenanceCheck.description,
                                assignedToID: maintenanceCheck.assignedToID,
                                expires: maintenanceCheck.expires, // the value of maintenanceCheck.expires here is already computed from upcomingScheduleDate()
                                status: maintenanceCheck.status,
                                startDate: maintenanceCheck.startDate,
                                isOverDue: isOverDueTask(maintenanceCheck),
                                isCompleted:
                                    maintenanceCheck.status === 'Completed'
                                        ? '1'
                                        : '2',
                            }
                        },
                    )
                    // Completed: sort by "expires" from recent to oldest
                    maintenanceChecksArray.sort((a: any, b: any) => {
                        if (a.isCompleted === '1' && b.isCompleted === '1') {
                            if (a.expires === 'NA' && b.expires !== 'NA') {
                                return 1
                            } else if (
                                a.expires !== 'NA' &&
                                b.expires === 'NA'
                            ) {
                                return -1
                            } else {
                                return (
                                    new Date(b.expires).getTime() -
                                    new Date(a.expires).getTime()
                                )
                            }
                        } else if (a.isCompleted === '1') {
                            return 1
                        } else if (b.isCompleted === '1') {
                            return -1
                        } else {
                            return dayjs(a.expires).diff(b.expires)
                        }
                    })
                    setCompletedRecurringTasks(maintenanceChecksArray)
                }
            },
            onError: (error: any) => {
                console.error('readComponentMaintenanceChecks error', error)
            },
        },
    )

    const loadCompletedRecurringTasks = async (recurringID: number) => {
        if (recurringID > 0) {
            await readComponentMaintenanceChecks({
                variables: {
                    filter: {
                        recurringID: {
                            eq: +recurringID,
                        },
                        status: { eq: 'Completed' },
                    },
                },
            })
        }
    }
    const handleOnChangeVessel = (value: any) => {
        const selectedVessel = vessels.find(
            (vessel: any) => vessel.id === value?.value,
        )
        setCurrentVessel(selectedVessel || null)
        setInventoryDefaultValue({
            label: null,
            value: '0',
        })
        setCurrentTask({
            ...currentTask,
            basicComponentID: value?.value,
            inventoryID: 0,
        })
        setInventories(
            inventories.filter((inventory: any) => {
                return +inventory.vessel.id === +value?.value
            }),
        )
        getEngineIdsByVessel({
            variables: {
                id: +value?.value,
            },
        })
        getVesselStatus({
            variables: {
                id: +value?.value,
            },
        })
    }

    const handleOnChangeInventory = (value: any) => {
        setCurrentTask({
            ...currentTask,
            inventoryID: value?.value,
        })
        setInventoryDefaultValue(value)
    }

    const handleSubtaskOnChangeInventory = (value: any) => {
        setSubtaskInventoryValue(value?.value || 0)
    }

    const handleDeleteLink = (link: any) => {
        setLinkSelectedOption(linkSelectedOption.filter((l: any) => l !== link))
    }
    const linkItem = (link: any) => {
        return (
            <div className="flex justify-between align-middle mr-2 w-fit">
                <Link href={link.label} target="_blank" className="ml-2 ">
                    {link.label}
                </Link>
                <div className="ml-2 ">
                    <CircleX
                        className="w-5 h-5 alert cursor-pointer"
                        onClick={() => handleDeleteLink(link)}
                    />
                </div>
            </div>
        )
    }
    useEffect(() => {
        if (maintenanceChecks) {
            loadCompletedRecurringTasks(+maintenanceChecks.recurringID)
        }
        getCategoryList({
            variables: {
                clientID: +(localStorage.getItem('clientId') ?? 0),
            },
        })
    }, [maintenanceChecks])

    // Load task records when component mounts or taskId changes
    useEffect(() => {
        if (taskId > 0) {
            queryTaskRecords({
                variables: {
                    id: taskId,
                },
                fetchPolicy: 'cache-and-network',
            })
        }
    }, [taskId])

    // Refresh task records when switching to Notes & updates tab (for better UX)
    useEffect(() => {
        if (activeTab === 'Notes & updates' && taskId > 0) {
            queryTaskRecords({
                variables: {
                    id: taskId,
                },
                fetchPolicy: 'cache-first',
            })
        }
    }, [activeTab, taskId])

    const [getCategoryList] = useLazyQuery(GetMaintenanceCategories, {
        fetchPolicy: 'cache-and-network',
        onCompleted: (response) => {
            const data = response.readMaintenanceCategories.nodes
            if (data) {
                setCategoryList(
                    data.map((category: any) => ({
                        label: category.name,
                        value: category.id,
                    })),
                )
            }
        },
        onError: (error) => {
            console.error('Error getting maintenance categories', error)
        },
    })

    // Reusable function to handle record editing
    const handleEditRecord = (record: any) => {
        // Use setTimeout to ensure state updates happen in the next tick
        setTimeout(() => {
            setReviewContent(record.description)
            setCommentData(record)
            setCommentTime(new Date(record.time))
            setOpenRecordsDialog(true)
        }, 0)
    }

    // Reusable function to handle adding new records
    const handleAddRecord = () => {
        // Use setTimeout to ensure state updates happen in the next tick
        setTimeout(() => {
            setReviewContent('')
            setCommentData(false)
            setCommentTime(false)
            setOpenRecordsDialog(true)
        }, 0)
    }

    const handleSaveRecords = () => {
        const variables = {
            input: {
                commentType: 'General',
                description: reviewContent,
                time: commentTime
                    ? dayjs(commentTime).format('DD/MM/YYYY HH:mm')
                    : dayjs().format('DD/MM/YYYY HH:mm'),
                authorID: commentData?.authorID,
                maintenanceCheckID: taskId,
                subTaskID: displayUpdateSubTask ? currentSubTaskCheckID : 0,
            },
        }

        if (commentData?.id > 0) {
            // Optimistic update for editing
            const updatedRecords = taskRecords.map((record) =>
                record.id === commentData.id
                    ? {
                          ...record,
                          description: reviewContent,
                          time: commentTime || record.time,
                      }
                    : record,
            )
            setTaskRecords(updatedRecords)

            updateTaskRecord({
                variables: {
                    input: {
                        id: commentData?.id,
                        ...variables.input,
                    },
                },
            })
        } else {
            // Optimistic update for creating
            const newRecord = {
                id: `temp-${Date.now()}`, // Temporary ID
                description: reviewContent,
                time: commentTime || new Date().toISOString(),
                author: {
                    id: variables.input.authorID,
                    firstName: 'You', // Placeholder
                    surname: '',
                },
                subTaskID: variables.input.subTaskID,
            }
            setTaskRecords([newRecord, ...taskRecords])

            createTaskRecord({
                variables: {
                    input: {
                        ...variables.input,
                    },
                },
            })
        }
        // Don't close dialog immediately - let the mutation onCompleted handle it
    }

    const [createTaskRecord] = useMutation(CreateMissionTimeline, {
        refetchQueries: () => [
            {
                query: GetTaskRecords,
                variables: { id: taskId },
            },
        ],
        awaitRefetchQueries: true,
        onCompleted: (response) => {
            setOpenRecordsDialog(false)
            // Clear form data
            setReviewContent('')
            setCommentData(false)
            setCommentTime(false)
            // Also manually refetch to ensure UI updates
            if (taskId > 0) {
                queryTaskRecords({
                    variables: { id: taskId },
                    fetchPolicy: 'network-only',
                })
            }
        },
        onError: (error) => {
            console.error('❌ Error creating Task Record', error)
            // Revert optimistic update on error
            if (taskId > 0) {
                queryTaskRecords({
                    variables: { id: taskId },
                    fetchPolicy: 'network-only',
                })
            }
        },
    })

    const [updateTaskRecord] = useMutation(UpdateMissionTimeline, {
        refetchQueries: () => [
            {
                query: GetTaskRecords,
                variables: { id: taskId },
            },
        ],
        awaitRefetchQueries: true,
        onCompleted: (response) => {
            setOpenRecordsDialog(false)
            // Clear form data
            setReviewContent('')
            setCommentData(false)
            setCommentTime(false)
            // Also manually refetch to ensure UI updates
            if (taskId > 0) {
                queryTaskRecords({
                    variables: { id: taskId },
                    fetchPolicy: 'network-only',
                })
            }
        },
        onError: (error) => {
            console.error('❌ Error updating task record', error)
            // Revert optimistic update on error
            if (taskId > 0) {
                queryTaskRecords({
                    variables: { id: taskId },
                    fetchPolicy: 'network-only',
                })
            }
        },
    })

    const [queryTaskRecords] = useLazyQuery(GetTaskRecords, {
        fetchPolicy: 'cache-and-network',
        notifyOnNetworkStatusChange: true,
        errorPolicy: 'all',
        onCompleted: (response) => {
            const data = response.readMissionTimelines.nodes

            if (data) {
                // Sort records by time to ensure consistent ordering
                const sortedData = [...data].sort(
                    (a, b) =>
                        new Date(b.time).getTime() - new Date(a.time).getTime(),
                )
                setTaskRecords(sortedData)
            } else {
                setTaskRecords([])
            }
        },
        onError: (error) => {
            console.error('❌ Error getting task records', error)
            setTaskRecords([]) // Clear records on error to prevent stale data
        },
    })

    const handleDeleteRecord = () => {
        // Optimistic update for deletion
        const filteredRecords = taskRecords.filter(
            (record) => record.id !== deleteRecordID,
        )
        setTaskRecords(filteredRecords)

        updateTaskRecord({
            variables: {
                input: {
                    id: +deleteRecordID,
                    archived: true,
                },
            },
        })
        setOpenDeleteRecordDialog(false)
    }

    const taskIsDateType = () => {
        if (currentTask.occursEveryType) {
            if (
                currentTask.occursEveryType === 'Days' ||
                currentTask.occursEveryType === 'Weeks' ||
                currentTask.occursEveryType === 'Months'
            ) {
                return true
            }
        } else {
            if (
                recurringTasks?.occursEveryType === 'Days' ||
                recurringTasks?.occursEveryType === 'Weeks' ||
                recurringTasks?.occursEveryType === 'Months'
            ) {
                return true
            }
        }
        return false
    }

    const taskIsEngineHourType = () => {
        if (currentTask?.occursEveryType) {
            if (currentTask.occursEveryType === 'Hours') {
                return true
            }
        } else {
            if (recurringTasks?.occursEveryType === 'Hours') {
                return true
            }
        }
        return false
    }

    const handleSetOccursEveryType = (value: any) => {
        setEngineList([])
        if (value === 'Days' || value === 'Weeks' || value === 'Months') {
            setCurrentTask({
                ...currentTask,
                occursEveryType: value,
            })
        } else if (
            maintenanceChecks?.basicComponentID > 0 ||
            currentTask?.basicComponentID > 0
        ) {
            setCurrentTask({
                ...currentTask,
                occursEveryType: value,
            })
            getEngineIdsByVessel({
                variables: {
                    id:
                        currentTask?.basicComponentID > 0
                            ? currentTask.basicComponentID
                            : maintenanceChecks.basicComponentID,
                },
            })
        } else {
            setCurrentTask({
                ...currentTask,
                occursEveryType: 'Days',
            })
            toast({
                title: 'Error',
                description: 'Please add a vessel to set this option',
                variant: 'destructive',
            })
        }
    }

    const [getEngineIdsByVessel] = useLazyQuery(GET_ENGINE_IDS_BY_VESSEL, {
        fetchPolicy: 'cache-and-network',
        onCompleted: (response: any) => {
            const data = response.readBasicComponents.nodes
            const engineIds = data.map((engine: any) => engine.id)
            engineIds &&
                engineIds.length > 0 &&
                queryGetEngines({
                    variables: {
                        id: engineIds,
                    },
                })
        },
        onError: (error: any) => {
            console.error('getEnginesByVessel error', error)
        },
    })

    const [queryGetEngines] = useLazyQuery(GET_ENGINES, {
        fetchPolicy: 'no-cache',
        onCompleted: (response: any) => {
            const data = response.readEngines.nodes
            setEngineList(data)
        },
        onError: (error: any) => {
            console.error('getEngines error', error)
        },
    })

    const disableSave = () => {
        setSaveDisabled(true)
    }

    const enableSave = () => {
        setSaveDisabled(false)
    }

    const handleCheckEngineCheck = (e: any, engineId: number) => {
        disableSave()
        setDisplayCheckEngineCheck({
            ...displayCheckEngineCheck,
            [engineId]: { value: e.target.checked },
        })
        if (
            recurringTasks?.engineUsage?.nodes?.find(
                (engine: any) => engine.engine.id === engineId,
            )
        ) {
            updateEngineUsage({
                variables: {
                    input: {
                        id: recurringTasks?.engineUsage?.nodes?.find(
                            (engine: any) => engine.engine.id === engineId,
                        ).id,
                        isScheduled: e.target.checked,
                    },
                },
            })
        } else {
            createEngineUsage({
                variables: {
                    input: {
                        engineID: +engineId,
                        maintenanceScheduleID: +recurringTasks.id,
                        isScheduled: e.target.checked,
                    },
                },
            })
        }
    }

    const [createEngineUsage] = useMutation(CreateEngine_Usage, {
        onCompleted: (response: any) => {
            const data = response.createEngine_Usage
            if (data.id > 0) {
                getRecurringTask()
                enableSave()
            }
        },
        onError: (error: any) => {
            console.error('createEngineUsage error', error)
        },
    })

    const [updateEngineUsage] = useMutation(UpdateEngine_Usage, {
        onCompleted: (response: any) => {
            const data = response.updateEngine_Usage
            if (data.id > 0) {
                getRecurringTask()
                enableSave()
            }
        },
        onError: (error: any) => {
            console.error('updateEngineUsage error', error)
        },
    })

    const getRecurringTask = async () => {
        await queryRecurringTask({
            variables: {
                id: +recurringTasks.id,
            },
        })
    }

    const [queryRecurringTask] = useLazyQuery(GET_RECURRING_TASK, {
        onCompleted: (response: any) => {
            const data = response.readOneMaintenanceSchedule
            if (data) {
                setRecurringTasks(data)
            }
        },
        onError: (error: any) => {
            console.error('queryRecurringTask error', error)
        },
    })

    const handleEngineHours = (e: any, engineId: number) => {
        if (
            recurringTasks?.engineUsage?.nodes?.find(
                (engine: any) => engine.engine.id === engineId,
            )
        ) {
            updateEngineUsage({
                variables: {
                    input: {
                        id: recurringTasks?.engineUsage?.nodes?.find(
                            (engine: any) => engine.engine.id === engineId,
                        ).id,
                        lastScheduleHours: +e.target.value,
                    },
                },
            })
        } else {
            createEngineUsage({
                variables: {
                    input: {
                        engineID: engineId,
                        maintenanceScheduleID: recurringTasks.id,
                        lastScheduleHours: +e.target.value,
                    },
                },
            })
        }
    }

    const handleChangeCategpory = (value: any) => {
        if (!value) return

        if (value.value === 'NEW_CATEGORY') {
            setCreateCategoryDialog(true)
        } else {
            setCurrentTask({
                ...currentTask,
                category: value.value,
            })
        }
    }

    const handleCreateCategory = () => {
        const category = (
            document.getElementById('task-new-category') as HTMLInputElement
        ).value
        createCategory({
            variables: {
                input: {
                    name: category,
                },
            },
        })
    }

    const [createCategory] = useMutation(CREATE_MAINTENANCE_CATEGORY, {
        onCompleted: (response: any) => {
            const data = response.createMaintenanceCategory
            if (data.id > 0) {
                getCategoryList({
                    variables: {
                        clientID: +(localStorage.getItem('clientId') ?? 0),
                    },
                })
                setCreateCategoryDialog(false)
                setCurrentTask({
                    ...currentTask,
                    category: data.id,
                })
            }
        },
        onError: (error: any) => {
            console.error('createCategoryEntry error', error)
        },
    })

    const isDateDisabled = () => {
        return (
            currentTask?.occursEveryType === 'Hours' ||
            currentTask?.occursEveryType === 'Uses' ||
            maintenanceChecks?.maintenanceSchedule?.occursEveryType ===
                'Hours' ||
            maintenanceChecks?.maintenanceSchedule?.occursEveryType === 'Uses'
        )
    }

    const filterInventories = (inventoryData: any) => {
        return inventoryData.filter(
            (inventory: any, index: number, self: any[]) =>
                index ===
                self.findIndex(
                    (i: any) =>
                        i?.item?.toLowerCase() ===
                        inventory?.item?.toLowerCase(),
                ),
        )
    }

    const handleSetVesselStatus = (status: boolean) => {
        setVesselStatus({
            ...vesselStatus,
            status: status ? 'OutOfService' : 'AvailableForVoyage',
        })
        status
            ? createVesselStatus({
                  variables: {
                      input: {
                          vesselID: currentTask?.basicComponentID,
                          date: dayjs().format('YYYY-MM-DD'),
                          status: 'OutOfService',
                          comment: vesselStatus?.comment,
                          reason: 'Other',
                          otherReason:
                              'From Maintenance Task ' +
                              maintenanceChecks?.name +
                              ' on ' +
                              dayjs().format('YYYY-MM-DD'),
                          expectedReturn: expiryDate,
                      },
                  },
              })
            : createVesselStatus({
                  variables: {
                      input: {
                          vesselID: currentTask?.basicComponentID,
                          date: dayjs().format('YYYY-MM-DD'),
                          status: 'AvailableForVoyage',
                      },
                  },
              })
    }

    const handleUpdateVesselStatus = () => {
        {
            vesselStatus?.status === 'OutOfService'
                ? createVesselStatus({
                      variables: {
                          input: {
                              vesselID: vesselStatus?.vesselID,
                              date: vesselStatus?.date,
                              status: vesselStatus?.status,
                              comment: vesselStatus?.comment,
                              reason: vesselStatus?.reason,
                              otherReason: vesselStatus?.otherReason,
                              expectedReturn: vesselStatus?.expectedReturn,
                          },
                      },
                  })
                : createVesselStatus({
                      variables: {
                          input: {
                              vesselID: vesselStatus?.vesselID,
                              date: vesselStatus?.date,
                              status: vesselStatus?.status,
                          },
                      },
                  })
        }
    }

    const handleVesselStatusDate = (date: any) => {
        setVesselStatus({
            ...vesselStatus,
            date: date,
        })
    }

    const handleVesselStatusReturnDate = (date: any) => {
        setVesselStatus({
            ...vesselStatus,
            expectedReturn: date,
        })
    }

    const [createVesselStatus] = useMutation(CREATE_VESSELSTATUS, {
        onCompleted: (response: any) => {
            const data = response.createVesselStatus
            setVesselStatus({
                ...vesselStatus,
                vesselID: data?.vesselID,
                date: data?.date,
                status: data?.status,
                comment: data?.comment,
                reason: data?.reason,
                otherReason: data?.otherReason,
                expectedReturn: data?.expectedReturn,
            })
            setDisplayEditStatus(false)
        },
        onError: (error: any) => {
            toast({
                title: 'Error',
                description: error.message,
                variant: 'destructive',
            })
        },
    })

    const handleVesselStatusChange = (value: any) => {
        setVesselStatus({
            ...vesselStatus,
            status: value?.value,
        })
    }

    const handleVesselStatusReasonChange = (value: any) => {
        setVesselStatus({
            ...vesselStatus,
            reason: value?.value,
        })
    }

    const handleScheduleTypeChange = (value: any) => {
        setScheduleType(value)
        if (value?.value === 'Recurring') {
            handleDisplayRecurringTasks({
                target: {
                    checked: true,
                },
            })
        } else {
            handleDisplayRecurringTasks({
                target: {
                    checked: false,
                },
            })
            setCurrentMaintenanceCheck({
                ...currentMaintenanceCheck,
                maintenanceSchedule: {
                    ...currentMaintenanceCheck.maintenanceSchedule,
                    occursEveryType: 'Days',
                },
            })
            handleSetOccursEveryType('Days')
        }
    }

    const handleDutyHoursDueChange = (e: any) => {
        setCurrentMaintenanceCheck({
            ...currentMaintenanceCheck,
            startHours: e.target.value,
        })
    }

    const [getFieldImages] = useLazyQuery(GET_SECTION_MEMBER_IMAGES, {
        fetchPolicy: 'cache-and-network',
        onCompleted: (response: any) => {
            const data = response.readCaptureImages.nodes
            if (data) {
                setFieldImages(data)
            }
        },
        onError: (error: any) => {
            console.error('getFieldImages error', error)
        },
    })

    const getFile = () => {
        if (fieldImages && Array.isArray(fieldImages)) {
            return fieldImages
                .filter((image: any) => image.fieldName === taskID)
                .sort((a: any, b: any) => b.id - a.id)
        }
        return false
    }

    return (
        <div className="space-y-6">
            {/* Task Header */}
            <ListHeader
                title={
                    maintenanceChecks?.name
                        ? `Task: ${maintenanceChecks.name}`
                        : `Task #${taskId}`
                }
            />

            {!isEmpty(completedRecurringTasks) && (
                <div className="flex items-center mx-2.5">
                    {taskTab !== 'task' && (
                        <Button
                            title="Completed Tasks"
                            className="hover:"
                            iconLeft="check"
                            onClick={() => {
                                setTaskTab('completed')
                            }}
                        />
                    )}
                    {taskTab === 'completed' && (
                        <Button
                            title="Task Details"
                            className="hover:"
                            iconLeft="back_arrow"
                            onClick={() => {
                                setTaskTab('task')
                            }}
                        />
                    )}
                </div>
            )}

            {taskTab === 'task' && (
                <>
                    {/* Basic Task Information Card */}
                    <Card className="mx-2.5">
                        <CardHeader>
                            <H4>Basic Information</H4>
                            <P>
                                Set the task title, assign it to a vessel or
                                inventory item, and configure the task type and
                                schedule.
                            </P>
                        </CardHeader>
                        <CardContent className="space-y-6">
                            <Label
                                className="w-full"
                                htmlFor="task-name"
                                label="Title">
                                <Input
                                    id="task-name"
                                    defaultValue={maintenanceChecks?.name}
                                    type="text"
                                    placeholder="Task name"
                                />
                            </Label>

                            <div className="grid grid-cols-1 md:grid-cols-2 gap-5">
                                {inSidebar ? (
                                    <Combobox
                                        label="Vessel"
                                        id="vessel-combobox"
                                        options={
                                            vessels && vessels.length > 0
                                                ? vessels.map(
                                                      (vessel: any) => ({
                                                          value: vessel.id,
                                                          label: vessel.title,
                                                          vessel: getVesselWithIcon(
                                                              vessel.id,
                                                              vessel,
                                                          ),
                                                      }),
                                                  )
                                                : []
                                        }
                                        buttonClassName="w-full"
                                        labelClassName="w-full"
                                        isLoading={
                                            !vessels || vessels.length === 0
                                        }
                                        defaultValues={
                                            currentVessel
                                                ? {
                                                      value: currentVessel.id,
                                                      label: currentVessel.title,
                                                  }
                                                : undefined
                                        }
                                        onChange={handleOnChangeVessel}
                                        placeholder="Select vessel"
                                    />
                                ) : (
                                    <Combobox
                                        id="vessel-combobox"
                                        label="Vessel"
                                        isLoading={
                                            !vessels || vessels.length === 0
                                        }
                                        options={
                                            vessels && vessels.length > 0
                                                ? vessels.map(
                                                      (vessel: any) => ({
                                                          value: vessel.id,
                                                          label: vessel.title,
                                                          vessel: getVesselWithIcon(
                                                              vessel.id,
                                                              vessel,
                                                          ),
                                                      }),
                                                  )
                                                : []
                                        }
                                        buttonClassName="w-full"
                                        labelClassName="w-full"
                                        value={
                                            currentVessel
                                                ? {
                                                      value: currentVessel.id,
                                                      label: currentVessel.title,
                                                  }
                                                : undefined
                                        }
                                        onChange={handleOnChangeVessel}
                                        placeholder="Select vessel"
                                    />
                                )}
                                <Combobox
                                    label="Inventory"
                                    isLoading={
                                        !inventories || inventories.length < 0
                                    }
                                    id="task-inventory"
                                    buttonClassName="w-full"
                                    labelClassName="w-full"
                                    options={
                                        inventories && inventories.length > 0
                                            ? filterInventories(
                                                  inventories,
                                              ).map((inventory: any) => ({
                                                  value: inventory.id,
                                                  label: inventory.item,
                                              }))
                                            : []
                                    }
                                    value={inventoryDefaultValue}
                                    onChange={handleOnChangeInventory}
                                    placeholder="Select inventory item"
                                />
                            </div>

                            <Combobox
                                id="task-category"
                                options={[
                                    {
                                        label: ' --- Add new category --- ',
                                        value: 'NEW_CATEGORY',
                                    },
                                    ...(categoryList && categoryList.length > 0
                                        ? categoryList
                                        : []),
                                ]}
                                label="Group to"
                                value={
                                    categoryList &&
                                    categoryList.length > 0 &&
                                    categoryList.filter(
                                        (option: any) =>
                                            option.value ===
                                            currentTask?.category,
                                    ).length > 0
                                        ? categoryList.filter(
                                              (option: any) =>
                                                  option.value ===
                                                  currentTask?.category,
                                          )[0]
                                        : categoryList &&
                                            categoryList.length > 0 &&
                                            categoryList.filter(
                                                (option: any) =>
                                                    option.value ===
                                                    maintenanceChecks
                                                        ?.maintenanceCategory
                                                        ?.id,
                                            )?.length > 0
                                          ? categoryList.filter(
                                                (option: any) =>
                                                    option.value ===
                                                    maintenanceChecks
                                                        ?.maintenanceCategory
                                                        ?.id,
                                            )[0]
                                          : undefined
                                }
                                placeholder="Select Category"
                                onChange={handleChangeCategpory}
                            />
                        </CardContent>
                    </Card>

                    {/* Task Schedule Card */}
                    <Card className="mx-2.5">
                        <CardHeader>
                            <H4>Task Schedule</H4>
                            <P>
                                Configure when this task is due and how it
                                should be scheduled.
                            </P>
                        </CardHeader>
                        <CardContent className="space-y-6">
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-5">
                                <Combobox
                                    id="task-type"
                                    options={[
                                        {
                                            label: 'Due by date',
                                            value: 'Expiry',
                                        },
                                        {
                                            label: 'Due by engine hours',
                                            value: 'EngineHours',
                                        },
                                        {
                                            label: 'Recurring task',
                                            value: 'Recurring',
                                        },
                                    ]}
                                    label="Task type"
                                    value={scheduleType}
                                    placeholder="Select Task Type"
                                    onChange={handleScheduleTypeChange}
                                />
                                <div>
                                    {scheduleType.value === 'Expiry' && (
                                        <DatePicker
                                            mode="single"
                                            label="Due date"
                                            type="date"
                                            disabled={isDateDisabled()}
                                            value={
                                                expiryDate && expiryDate !== ''
                                                    ? expiryDate.toDate
                                                        ? expiryDate.toDate()
                                                        : new Date(expiryDate)
                                                    : undefined
                                            }
                                            onChange={(newDate) => {
                                                if (
                                                    maintenanceChecks
                                                        ?.maintenanceSchedule
                                                        ?.occursEveryType ===
                                                    'Hours'
                                                ) {
                                                    toast({
                                                        title: 'Error',
                                                        description:
                                                            'This task has recurring based on engine hours and not allow the date edit',
                                                        variant: 'destructive',
                                                    })
                                                } else {
                                                    handleExpiryChange(newDate)
                                                }
                                            }}
                                            placeholder="Select due date"
                                            clearable={true}
                                        />
                                    )}
                                    {scheduleType.value === 'EngineHours' && (
                                        <Label
                                            className="w-full"
                                            htmlFor="due-engine-hours"
                                            label="Engine hours">
                                            <Input
                                                id="due-engine-hours"
                                                defaultValue={
                                                    currentMaintenanceCheck?.startHours
                                                        ? currentMaintenanceCheck.startHours
                                                        : ''
                                                }
                                                type="number"
                                                placeholder="Enter engine hours"
                                                onChange={(e) => {
                                                    handleDutyHoursDueChange(e)
                                                }}
                                            />
                                        </Label>
                                    )}
                                    {(currentTask?.occursEveryType ===
                                        'Hours' ||
                                        currentTask?.occursEveryType ===
                                            'Uses') && (
                                        <P className="mt-9 text-destructive">
                                            This task has a recurring period
                                            based on the number of hours/uses
                                        </P>
                                    )}
                                </div>
                            </div>
                        </CardContent>
                    </Card>
                    {/* Task Details Tabs */}
                    <Card className="mx-2.5">
                        <CardHeader>
                            <H4>Task Details</H4>
                            <P>
                                Manage task details, sub-tasks, attachments, and
                                recurring schedules.
                            </P>
                        </CardHeader>
                        <CardContent className="space-y-6">
                            <div className="w-full">
                                {isDesktop ? (
                                    <Tabs
                                        value={activeTab}
                                        onValueChange={setActiveTab}>
                                        <ScrollArea className="w-full bg-card/0 phablet:bg-card/0 pb-2">
                                            <TabsList className="inline-flex w-max px-1">
                                                <TabsTrigger value="Details">
                                                    Details
                                                </TabsTrigger>
                                                <TabsTrigger value="Sub-tasks">
                                                    Sub-tasks
                                                </TabsTrigger>
                                                <TabsTrigger value="Links-docs">
                                                    Links-docs-images
                                                </TabsTrigger>
                                                {displayRecurringTasks && (
                                                    <TabsTrigger value="Recurring schedule">
                                                        Recurring schedule
                                                    </TabsTrigger>
                                                )}
                                                <TabsTrigger value="Notes & updates">
                                                    Notes & updates
                                                </TabsTrigger>
                                            </TabsList>
                                        </ScrollArea>
                                    </Tabs>
                                ) : (
                                    <div className="space-y-4">
                                        <div className="flex flex-col space-y-2">
                                            <Button
                                                variant={
                                                    activeTab === 'Details'
                                                        ? 'primary'
                                                        : 'outline'
                                                }
                                                onClick={() =>
                                                    setActiveTab('Details')
                                                }
                                                className="justify-start">
                                                Details
                                            </Button>
                                            <Button
                                                variant={
                                                    activeTab === 'Sub-tasks'
                                                        ? 'primary'
                                                        : 'outline'
                                                }
                                                onClick={() =>
                                                    setActiveTab('Sub-tasks')
                                                }
                                                className="justify-start">
                                                Sub-tasks
                                            </Button>
                                            <Button
                                                variant={
                                                    activeTab === 'Links-docs'
                                                        ? 'primary'
                                                        : 'outline'
                                                }
                                                onClick={() =>
                                                    setActiveTab('Links-docs')
                                                }
                                                className="justify-start">
                                                Links-docs-images
                                            </Button>
                                            {displayRecurringTasks && (
                                                <Button
                                                    variant={
                                                        activeTab ===
                                                        'Recurring schedule'
                                                            ? 'primary'
                                                            : 'outline'
                                                    }
                                                    onClick={() =>
                                                        setActiveTab(
                                                            'Recurring schedule',
                                                        )
                                                    }
                                                    className="justify-start">
                                                    Recurring schedule
                                                </Button>
                                            )}
                                            <Button
                                                variant={
                                                    activeTab ===
                                                    'Notes & updates'
                                                        ? 'primary'
                                                        : 'outline'
                                                }
                                                onClick={() =>
                                                    setActiveTab(
                                                        'Notes & updates',
                                                    )
                                                }
                                                className="justify-start">
                                                Notes & updates
                                            </Button>
                                        </div>
                                    </div>
                                )}
                            </div>
                            {activeTab === 'Details' && (
                                <>
                                    <div className="space-y-4">
                                        <Label
                                            htmlFor="task-description"
                                            label="Task Description">
                                            {maintenanceChecks &&
                                                maintenanceChecks.id && (
                                                    <Editor
                                                        id="task-description"
                                                        placeholder="Task description"
                                                        className={''}
                                                        content={content}
                                                        handleEditorChange={
                                                            handleEditorChange
                                                        }
                                                    />
                                                )}
                                        </Label>
                                        {/* <UploadCloudFlare
                                            files={attachments}
                                            setFiles={setAttachments}
                                        />
                                        <div className="w-full flex flex-col space-y-2"> */}
                                        <CloudFlareCaptures
                                            inputId={1}
                                            sectionId={+taskID}
                                            buttonType={'button'}
                                            sectionName={
                                                'componentMaintenanceCheckID'
                                            }
                                        />
                                        {/* </div> */}
                                        <Label
                                            label="Reference"
                                            htmlFor="task-workorder">
                                            <Input
                                                id={`task-workorder`}
                                                defaultValue={
                                                    maintenanceChecks?.workOrderNumber
                                                }
                                                type="text"
                                                placeholder="Work order/Reference"
                                            />
                                        </Label>
                                        <Combobox
                                            label="Allocated to:"
                                            options={
                                                crewMembers
                                                    ? crewMembers.map(
                                                          (member: any) => ({
                                                              value: member.id,
                                                              label: `${member.firstName ?? ''} ${member.surname ?? ''}`,
                                                              crew: member,
                                                              profile: {
                                                                  firstName:
                                                                      member.firstName,
                                                                  surname:
                                                                      member.surname,
                                                                  avatar: null,
                                                              },
                                                          }),
                                                      )
                                                    : []
                                            }
                                            isLoading={
                                                !crewMembers ||
                                                crewMembers.length === 0
                                            }
                                            defaultValues={
                                                maintenanceChecks?.assignedTo && {
                                                    label: `${maintenanceChecks.assignedTo.firstName ?? ''} ${maintenanceChecks.assignedTo.surname ?? ''}`,
                                                    value: maintenanceChecks
                                                        .assignedTo.id,
                                                }
                                            }
                                            onChange={(value: any) =>
                                                setCurrentTask({
                                                    ...currentTask,
                                                    assignees: value.value,
                                                })
                                            }
                                            placeholder="Select team"
                                        />
                                        <P>
                                            An email will be sent to the
                                            allocated team with order reference
                                            (if any) and details of this task.
                                        </P>

                                        <Combobox
                                            id="task-priority"
                                            label="Priority"
                                            options={priorityOptions}
                                            isLoading={!inventories}
                                            defaultValues={
                                                priorityOptions
                                                    .filter(
                                                        (option: any) =>
                                                            option.value ===
                                                            maintenanceChecks?.severity,
                                                    )
                                                    .map((option: any) => ({
                                                        value: option.value,
                                                        label: option.label,
                                                    }))[0]
                                            }
                                            placeholder="Select priority"
                                            onChange={(value: any) =>
                                                setCurrentTask({
                                                    ...currentTask,
                                                    severity: value.value,
                                                })
                                            }
                                        />
                                    </div>
                                </>
                            )}
                            {activeTab === 'Sub-tasks' && (
                                <>
                                    {subTasks.length > 0 && (
                                        <>
                                            <div className="flex items-center gap-4">
                                                <Progress
                                                    value={subtaskProgress}
                                                    className="h-2"
                                                />
                                                <span className="leading-none flex items-center">
                                                    {subtaskProgress > 0 && (
                                                        <span>
                                                            {subtaskProgress}%
                                                        </span>
                                                    )}
                                                </span>
                                            </div>

                                            <div className="space-y-3">
                                                {subTasks.map(
                                                    (subtask: any) => {
                                                        const inventory =
                                                            inventories?.find(
                                                                (i: any) =>
                                                                    i.id ===
                                                                    subtask
                                                                        .maintenanceScheduleSubTask
                                                                        .inventoryID,
                                                            )

                                                        return (
                                                            <div
                                                                key={`${subtask.id}-subtask`}>
                                                                <div className="flex items-start gap-3 flex-1">
                                                                    <CheckFieldLabel
                                                                        id={
                                                                            subtask.id
                                                                        }
                                                                        checked={
                                                                            subtask.status ===
                                                                            'Completed'
                                                                        }
                                                                        variant="success"
                                                                        onCheckedChange={(
                                                                            checked,
                                                                        ) => {
                                                                            handleUpdateSubTask(
                                                                                {
                                                                                    target: {
                                                                                        id: subtask.id,
                                                                                        checked:
                                                                                            checked ===
                                                                                            true,
                                                                                    },
                                                                                },
                                                                            )
                                                                        }}
                                                                        className="w-full">
                                                                        <div className="w-full flex justify-between gap-2.5 items-center">
                                                                            <div className="flex gap-2.5">
                                                                                <div>
                                                                                    {
                                                                                        subtask
                                                                                            .maintenanceScheduleSubTask
                                                                                            .task
                                                                                    }
                                                                                </div>
                                                                                {(subtask
                                                                                    ?.completedBy
                                                                                    ?.firstName ||
                                                                                    subtask?.dateCompleted) && (
                                                                                    <div className="text-sm text-muted-foreground flex items-center gap-2">
                                                                                        {subtask.status ===
                                                                                            'Completed' &&
                                                                                            subtask
                                                                                                ?.completedBy
                                                                                                ?.firstName && (
                                                                                                <Avatar
                                                                                                    size="xs"
                                                                                                    variant="success">
                                                                                                    <AvatarFallback>
                                                                                                        {getCrewInitials(
                                                                                                            subtask
                                                                                                                .completedBy
                                                                                                                .firstName,
                                                                                                            subtask
                                                                                                                .completedBy
                                                                                                                .surname,
                                                                                                        )}
                                                                                                    </AvatarFallback>
                                                                                                </Avatar>
                                                                                            )}
                                                                                        <div>
                                                                                            {bp[
                                                                                                'tablet-sm'
                                                                                            ] &&
                                                                                                subtask
                                                                                                    ?.completedBy
                                                                                                    ?.firstName && (
                                                                                                    <span>
                                                                                                        {
                                                                                                            subtask
                                                                                                                .completedBy
                                                                                                                .firstName
                                                                                                        }{' '}
                                                                                                        {
                                                                                                            subtask
                                                                                                                .completedBy
                                                                                                                .surname
                                                                                                        }
                                                                                                    </span>
                                                                                                )}
                                                                                            {bp[
                                                                                                'tablet-lg'
                                                                                            ] &&
                                                                                                subtask?.dateCompleted && (
                                                                                                    <span>
                                                                                                        {
                                                                                                            ' - '
                                                                                                        }
                                                                                                        {formatDate(
                                                                                                            subtask.dateCompleted,
                                                                                                        )}
                                                                                                    </span>
                                                                                                )}
                                                                                        </div>
                                                                                    </div>
                                                                                )}
                                                                            </div>

                                                                            {inventory && (
                                                                                <Badge
                                                                                    variant="outline"
                                                                                    className="h-fit py-1 px-2 text-sm"
                                                                                    type="normal">
                                                                                    {
                                                                                        inventory?.item
                                                                                    }
                                                                                </Badge>
                                                                            )}

                                                                            {subtask.findings && (
                                                                                <TooltipProvider>
                                                                                    <Tooltip>
                                                                                        <TooltipTrigger
                                                                                            className="p-0 h-fit"
                                                                                            asChild>
                                                                                            <Button
                                                                                                variant="link"
                                                                                                size="md"
                                                                                                onClick={(
                                                                                                    e,
                                                                                                ) => {
                                                                                                    e.stopPropagation()
                                                                                                    setSelectedSubtaskFindings(
                                                                                                        subtask,
                                                                                                    )
                                                                                                    setOpenSubTaskFindingsDialog(
                                                                                                        true,
                                                                                                    )
                                                                                                }}>
                                                                                                {getResponsiveLabel(
                                                                                                    bp.small,
                                                                                                    'findings',
                                                                                                    'View findings',
                                                                                                )}
                                                                                            </Button>
                                                                                        </TooltipTrigger>
                                                                                        <TooltipContent>
                                                                                            <p>
                                                                                                View
                                                                                                detailed
                                                                                                findings
                                                                                            </p>
                                                                                        </TooltipContent>
                                                                                    </Tooltip>
                                                                                </TooltipProvider>
                                                                            )}
                                                                        </div>
                                                                    </CheckFieldLabel>
                                                                </div>
                                                            </div>
                                                        )
                                                    },
                                                )}
                                            </div>
                                        </>
                                    )}
                                    <div className="my-4 flex">
                                        <Button
                                            variant="primary"
                                            onClick={() => {
                                                setOpenSubTaskDialog(true)
                                                setSubtaskContent('')
                                            }}>
                                            Add sub-task
                                        </Button>
                                    </div>
                                </>
                            )}
                            {activeTab === 'Links-docs' && (
                                <>
                                    <Label label="Links">
                                        {vessels && (
                                            <Input
                                                id="task-title"
                                                type="text"
                                                className={''}
                                                placeholder="Type a link and press Enter"
                                                onKeyDown={async (
                                                    event: React.KeyboardEvent<HTMLInputElement>,
                                                ) => {
                                                    if (event.key === 'Enter') {
                                                        const inputValue = (
                                                            event.target as HTMLInputElement
                                                        ).value
                                                        await createSeaLogsFileLinks(
                                                            {
                                                                variables: {
                                                                    input: {
                                                                        link: inputValue,
                                                                    },
                                                                },
                                                            },
                                                        )
                                                        ;(
                                                            event.target as HTMLInputElement
                                                        ).value = '' // Clear input value
                                                    }
                                                }}
                                            />
                                        )}
                                    </Label>
                                    <div className="my-4 flex">
                                        {linkSelectedOption
                                            ? linkSelectedOption.map(
                                                  (link: any) => (
                                                      <div key={link.value}>
                                                          {linkItem(link)}
                                                      </div>
                                                  ),
                                              )
                                            : fileLinks.map((link: any) => (
                                                  <div key={link.value}>
                                                      {linkItem(link)}
                                                  </div>
                                              ))}
                                    </div>
                                    <Separator className="my-4 w-full" />
                                    <div>
                                        <Label label="Documents and images">
                                            <FileUpload
                                                setDocuments={setDocuments}
                                                text=""
                                                subText="Drag files here or upload"
                                                documents={documents}
                                            />
                                        </Label>
                                        <div className="my-4">
                                            {documents.length > 0 &&
                                                documents.map(
                                                    (document: any) => (
                                                        <div
                                                            key={document.id}
                                                            className="flex items-center gap-8 justify-between p-2.5 rounded-lg border mb-4">
                                                            <FileItem
                                                                document={
                                                                    document
                                                                }
                                                            />
                                                            <Button
                                                                size={'icon'}
                                                                variant={
                                                                    'destructive'
                                                                }
                                                                onClick={() =>
                                                                    deleteFile(
                                                                        document.id,
                                                                    )
                                                                }>
                                                                <CircleX />
                                                            </Button>
                                                        </div>
                                                    ),
                                                )}
                                        </div>
                                    </div>
                                </>
                            )}
                            {displayRecurringTasks && (
                                <>
                                    {!edit_recurring_task ? (
                                        <Loading errorMessage="Oops You do not have the permission to view this section." />
                                    ) : (
                                        <>
                                            {activeTab ===
                                                'Recurring schedule' && (
                                                <>
                                                    <Label label="Schedule details" />

                                                    <Label
                                                        htmlFor="task-frequency"
                                                        label="Occurs every">
                                                        <Input
                                                            id="task-frequency"
                                                            defaultValue={
                                                                recurringTasks
                                                                    ? recurringTasks.occursEvery
                                                                    : '1'
                                                            }
                                                            type="number"
                                                            placeholder="Schedule every"
                                                            min={1}
                                                            onChange={(e) => {
                                                                setCurrentMaintenanceCheck(
                                                                    {
                                                                        ...currentMaintenanceCheck,
                                                                        maintenanceSchedule:
                                                                            {
                                                                                ...currentMaintenanceCheck.maintenanceSchedule,
                                                                                occursEvery:
                                                                                    e
                                                                                        .target
                                                                                        .value ??
                                                                                    '1',
                                                                            },
                                                                    },
                                                                )
                                                                setScheduleEvery(
                                                                    e.target
                                                                        .value,
                                                                )
                                                                updateDueDate()
                                                            }}
                                                        />
                                                    </Label>
                                                    <Combobox
                                                        id="task-recurring-type"
                                                        label="Occurrence type"
                                                        options={recurringType}
                                                        value={
                                                            currentTask?.occursEveryType
                                                                ? recurringType
                                                                      .filter(
                                                                          (
                                                                              option: any,
                                                                          ) =>
                                                                              option.value ===
                                                                              currentTask.occursEveryType,
                                                                      )
                                                                      .map(
                                                                          (
                                                                              option: any,
                                                                          ) => ({
                                                                              value: option.value,
                                                                              label: option.label,
                                                                          }),
                                                                      )[0]
                                                                : recurringType
                                                                      .filter(
                                                                          (
                                                                              option: any,
                                                                          ) =>
                                                                              option.value ===
                                                                              recurringTasks.occursEveryType,
                                                                      )
                                                                      .map(
                                                                          (
                                                                              option: any,
                                                                          ) => ({
                                                                              value: option.value,
                                                                              label: option.label,
                                                                          }),
                                                                      )[0]
                                                        }
                                                        placeholder="Select type"
                                                        onChange={(
                                                            value: any,
                                                        ) => {
                                                            if (!value) return
                                                            setCurrentMaintenanceCheck(
                                                                {
                                                                    ...currentMaintenanceCheck,
                                                                    maintenanceSchedule:
                                                                        {
                                                                            ...currentMaintenanceCheck.maintenanceSchedule,
                                                                            occursEveryType:
                                                                                value.value,
                                                                        },
                                                                },
                                                            )
                                                            handleSetOccursEveryType(
                                                                value.value,
                                                            )
                                                        }}
                                                    />
                                                    {taskIsDateType() && (
                                                        <DatePicker
                                                            mode="single"
                                                            type="date"
                                                            label="Last completed date"
                                                            value={
                                                                startDate
                                                                    ? startDate.toDate()
                                                                    : lastScheduleDate().toDate()
                                                            }
                                                            onChange={
                                                                handleStartDateChange
                                                            }
                                                            placeholder="Enter last schedule date"
                                                            disabled={
                                                                !edit_recurring_task
                                                            }
                                                        />
                                                    )}
                                                    {taskIsEngineHourType() && (
                                                        <>
                                                            {engineList.length >
                                                                0 &&
                                                                engineList.map(
                                                                    (
                                                                        engine: any,
                                                                    ) => (
                                                                        <div className="my-4">
                                                                            <div className="flex w-full gap-4 items-center">
                                                                                <div className=" w-1/2 ">
                                                                                    <Label
                                                                                        htmlFor={`check_engine-${engine.id}`}
                                                                                        data-ripple="true"
                                                                                        data-ripple-color="dark"
                                                                                        data-ripple-dark="true"
                                                                                        label={`${engine.title}${engine.type ? ' - ' + engine.type : ''}`}>
                                                                                        <Input
                                                                                            type="checkbox"
                                                                                            id={`check_engine-${engine.id}`}
                                                                                            onChange={(
                                                                                                e,
                                                                                            ) =>
                                                                                                handleCheckEngineCheck(
                                                                                                    e,
                                                                                                    engine.id,
                                                                                                )
                                                                                            }
                                                                                            checked={
                                                                                                displayCheckEngineCheck[
                                                                                                    engine
                                                                                                        .id
                                                                                                ]
                                                                                                    ? displayCheckEngineCheck[
                                                                                                          engine
                                                                                                              .id
                                                                                                      ]
                                                                                                          .value
                                                                                                    : recurringTasks?.engineUsage?.nodes?.find(
                                                                                                          (
                                                                                                              engineUsage: any,
                                                                                                          ) =>
                                                                                                              engineUsage
                                                                                                                  .engine
                                                                                                                  .id ===
                                                                                                              engine.id,
                                                                                                      )
                                                                                                          ?.isScheduled
                                                                                            }
                                                                                        />
                                                                                        <span className="absolute transition-opacity opacity-0 pointer-events-none top-2/4 left-1/3 -translate-y-2/4 -translate-x-2/4 peer-checked:opacity-100"></span>
                                                                                        <span className="ml-3">
                                                                                            <div>
                                                                                                <span className="">
                                                                                                    Engine
                                                                                                    Hours:{' '}
                                                                                                </span>
                                                                                                {
                                                                                                    engine.currentHours
                                                                                                }
                                                                                            </div>
                                                                                        </span>
                                                                                    </Label>
                                                                                </div>
                                                                                <div className="w-full">
                                                                                    <Input
                                                                                        id={`check_engine_hours-${engine.id}`}
                                                                                        name="check_engine_hours"
                                                                                        type="number"
                                                                                        min={
                                                                                            0
                                                                                        }
                                                                                        placeholder="Enter last schedule hours"
                                                                                        onChange={(
                                                                                            e,
                                                                                        ) =>
                                                                                            setEngineHours(
                                                                                                {
                                                                                                    ...engineHours,
                                                                                                    [engine.id]:
                                                                                                        e
                                                                                                            .target
                                                                                                            .value,
                                                                                                },
                                                                                            )
                                                                                        }
                                                                                        onBlur={(
                                                                                            e,
                                                                                        ) =>
                                                                                            handleEngineHours(
                                                                                                e,
                                                                                                engine.id,
                                                                                            )
                                                                                        }
                                                                                        value={
                                                                                            engineHours[
                                                                                                engine
                                                                                                    .id
                                                                                            ]
                                                                                                ? engineHours[
                                                                                                      engine
                                                                                                          .id
                                                                                                  ]
                                                                                                : recurringTasks?.engineUsage?.nodes?.find(
                                                                                                      (
                                                                                                          engineUsage: any,
                                                                                                      ) =>
                                                                                                          engineUsage
                                                                                                              .engine
                                                                                                              .id ===
                                                                                                          engine.id,
                                                                                                  )
                                                                                                      ?.lastScheduleHours
                                                                                        }
                                                                                        className={
                                                                                            ''
                                                                                        }
                                                                                        aria-describedby="expiry-last-schedule-error"
                                                                                        required
                                                                                    />
                                                                                </div>
                                                                            </div>
                                                                        </div>
                                                                    ),
                                                                )}
                                                        </>
                                                    )}
                                                    <Textarea
                                                        id="recurring-task-description"
                                                        rows={4}
                                                        defaultValue={stripHtmlTags(
                                                            recurringTasks
                                                                ? recurringTasks.description
                                                                : '',
                                                        )}
                                                        placeholder="Description and notes"
                                                    />
                                                    <Label
                                                        htmlFor="task-reminders"
                                                        data-ripple="true"
                                                        data-ripple-color="dark"
                                                        data-ripple-dark="true"
                                                        leftContent={
                                                            <Checkbox
                                                                id="task-reminders"
                                                                onCheckedChange={
                                                                    handleDisplayWarnings
                                                                }
                                                                checked={
                                                                    displayWarnings
                                                                }
                                                                isRadioStyle
                                                                size="lg"
                                                            />
                                                        }
                                                        label="CREATE YOUR OWN REMINDERS (Optional)"
                                                    />
                                                    {displayWarnings && (
                                                        <>
                                                            <Separator className="my-6" />

                                                            <Label
                                                                label="Low warning within (days)"
                                                                htmlFor="low-warn-within">
                                                                <Input
                                                                    type="number"
                                                                    id="low-warn-within"
                                                                    placeholder="Low warning within"
                                                                    disabled
                                                                    value={
                                                                        (currentTask.occursEveryType
                                                                            ? currentTask.occursEveryType
                                                                            : recurringTasks.occursEveryType) ===
                                                                        'Months'
                                                                            ? '14'
                                                                            : (
                                                                                    currentTask.occursEveryType
                                                                                        ? currentTask.occursEveryType
                                                                                        : recurringTasks.occursEveryType
                                                                                )
                                                                              ? '7'
                                                                              : '0'
                                                                    }
                                                                />
                                                            </Label>

                                                            <Label
                                                                label="Medium warning within (days)"
                                                                htmlFor="medium-warn-within">
                                                                <Input
                                                                    type="number"
                                                                    id="medium-warn-within"
                                                                    placeholder="Medium warning within"
                                                                    disabled
                                                                    value={
                                                                        (currentTask.occursEveryType
                                                                            ? currentTask.occursEveryType
                                                                            : recurringTasks.occursEveryType) ===
                                                                        'Months'
                                                                            ? '7'
                                                                            : (
                                                                                    currentTask.occursEveryType
                                                                                        ? currentTask.occursEveryType
                                                                                        : recurringTasks.occursEveryType
                                                                                )
                                                                              ? '3'
                                                                              : '0'
                                                                    }
                                                                />
                                                            </Label>

                                                            <Label
                                                                label="High warning within (days)"
                                                                htmlFor="high-warn-within">
                                                                <Input
                                                                    type="number"
                                                                    id="high-warn-within"
                                                                    placeholder="High warning within"
                                                                    disabled
                                                                    value={
                                                                        (currentTask.occursEveryType
                                                                            ? currentTask.occursEveryType
                                                                            : recurringTasks.occursEveryType) ===
                                                                        'Months'
                                                                            ? '3'
                                                                            : (
                                                                                    currentTask.occursEveryType
                                                                                        ? currentTask.occursEveryType
                                                                                        : recurringTasks.occursEveryType
                                                                                )
                                                                              ? '1'
                                                                              : '0'
                                                                    }
                                                                />
                                                            </Label>
                                                        </>
                                                    )}
                                                </>
                                            )}
                                        </>
                                    )}
                                </>
                            )}
                            {activeTab === 'Task costs' && (
                                <>
                                    <p className="   max-w-[40rem] leading-loose">
                                        Track the expected and actual costs of
                                        repairs and maintenance in SeaLogs
                                        reports module.
                                    </p>
                                    <div className="my-4 flex flex-col w-full">
                                        <Label
                                            htmlFor="task-projected"
                                            label="Projected costs">
                                            <Input
                                                id="task-projected"
                                                defaultValue={
                                                    maintenanceChecks?.projected
                                                }
                                                type="number"
                                                placeholder="Projected"
                                                onChange={updateCostsDifference}
                                            />
                                        </Label>
                                    </div>
                                    <div className="my-4 flex flex-col w-full">
                                        <Label
                                            htmlFor="task-actual"
                                            label="Actual costs">
                                            <Input
                                                id="task-actual"
                                                defaultValue={
                                                    maintenanceChecks?.actual
                                                }
                                                type="number"
                                                placeholder="Actual"
                                                onChange={updateCostsDifference}
                                            />
                                        </Label>
                                    </div>
                                    <div className="my-4 flex flex-col w-full">
                                        <Label
                                            htmlFor="task-difference"
                                            label="Difference">
                                            <div
                                                id="task-difference"
                                                className="w-full p-2 border-t border-dashed">
                                                {costsDifference}
                                            </div>
                                        </Label>
                                    </div>
                                </>
                            )}
                            {activeTab === 'Notes & updates' && (
                                <>
                                    {taskRecords.length > 0 && (
                                        <div className="space-y-2.5">
                                            {taskRecords.map((record: any) => (
                                                <RecordCard
                                                    key={`${record.id}-record-${record.time}`}
                                                    record={record}
                                                    onEdit={handleEditRecord}
                                                    onDelete={(recordId) => {
                                                        setDeleteRecordID(
                                                            recordId,
                                                        )
                                                        setOpenDeleteRecordDialog(
                                                            true,
                                                        )
                                                    }}
                                                />
                                            ))}
                                        </div>
                                    )}
                                    {taskRecords.length === 0 && (
                                        <div className="text-center py-8 text-muted-foreground">
                                            <p>No notes or updates yet.</p>
                                            <p className="text-sm">
                                                Click "Add note" to create the
                                                first one.
                                            </p>
                                        </div>
                                    )}
                                    {allImages.length > 0 && (
                                        <div className="flex flex-wrap mb-4">
                                            {allImages.map(
                                                (img: any, index: number) => (
                                                    <div
                                                        className="w-1/5 p-1 rounded-md relative"
                                                        key={index}>
                                                        <img
                                                            key={index}
                                                            src={img.imageData}
                                                            alt={`Captured ${index}`}
                                                            className="object-cover rounded-md"
                                                        />
                                                    </div>
                                                ),
                                            )}
                                        </div>
                                    )}
                                    <div className="mt-2 flex justify-between items-center gap-4">
                                        <Button
                                            variant="outline"
                                            iconLeft={Plus}
                                            onClick={handleAddRecord}>
                                            Add note
                                        </Button>
                                        <UploadCloudFlareCaptures
                                            file={getFile()}
                                            setFile={refreshImages}
                                            inputId={taskID.toString()}
                                            buttonType={'button'}
                                            sectionData={{
                                                id: +taskID,
                                                sectionName:
                                                    'componentMaintenanceCheckID',
                                            }}
                                        />
                                    </div>
                                </>
                            )}
                            <Separator className="my-6" />
                            <div className="pb-6 flex flex-col sm:flex-row gap-4">
                                <Combobox
                                    id="task-status"
                                    label="Task status"
                                    options={statusOptions || []}
                                    isLoading={
                                        !inventories ||
                                        !statusOptions ||
                                        statusOptions.length === 0
                                    }
                                    defaultValues={
                                        statusOptions &&
                                        maintenanceChecks?.status
                                            ? statusOptions
                                                  .filter(
                                                      (option: any) =>
                                                          option.value ===
                                                          maintenanceChecks?.status.replaceAll(
                                                              '_',
                                                              ' ',
                                                          ),
                                                  )
                                                  .map((option: any) => ({
                                                      value: option.value,
                                                      label: option.label,
                                                  }))[0]
                                            : undefined
                                    }
                                    value={
                                        statusOptions && currentTask?.status
                                            ? statusOptions
                                                  .filter(
                                                      (option: any) =>
                                                          option.value ===
                                                          currentTask?.status.replaceAll(
                                                              '_',
                                                              ' ',
                                                          ),
                                                  )
                                                  .map((option: any) => ({
                                                      value: option.value,
                                                      label: option.label,
                                                  }))[0]
                                            : undefined
                                    }
                                    placeholder="Select status"
                                    onChange={(value: any) => {
                                        if (!value) return

                                        if (
                                            !complete_task &&
                                            value.value === 'Completed'
                                        ) {
                                            toast({
                                                title: 'Error',
                                                description:
                                                    'You do not have the permission to complete this task.',
                                                variant: 'destructive',
                                            })
                                            return
                                        }
                                        setCurrentTask({
                                            ...currentTask,
                                            status: value.value.replaceAll(
                                                '_',
                                                ' ',
                                            ),
                                        })
                                    }}
                                    labelClassName="w-full"
                                />
                                {currentTask?.status === 'Completed' && (
                                    <DatePicker
                                        mode="single"
                                        type="date"
                                        label="Completion date"
                                        value={
                                            completionDate
                                                ? completionDate instanceof Date
                                                    ? completionDate
                                                    : typeof completionDate.toDate ===
                                                        'function'
                                                      ? completionDate.toDate()
                                                      : new Date(
                                                            completionDate.toString(),
                                                        )
                                                : undefined
                                        }
                                        className="w-full"
                                        wrapperClassName="w-full"
                                        onChange={handleCompletionChange}
                                        placeholder="Enter completion date"
                                        disabled={!complete_task}
                                        clearable={true}
                                    />
                                )}
                                {currentTask?.status === 'Completed' &&
                                    scheduleType.value === 'EngineHours' && (
                                        <Label
                                            className=" w-full"
                                            htmlFor="due-engine-hours"
                                            label="Engine hours">
                                            <Input
                                                id="due-engine-hours"
                                                type="number"
                                                placeholder="Enter engine hours"
                                                value={
                                                    currentMaintenanceCheck?.hoursCompleted ||
                                                    ''
                                                }
                                                onChange={(e) => {
                                                    setCurrentMaintenanceCheck({
                                                        ...currentMaintenanceCheck,
                                                        hoursCompleted:
                                                            e.target.value,
                                                    })
                                                    setCurrentTask({
                                                        ...currentTask,
                                                        hoursCompleted:
                                                            e.target.value,
                                                    })
                                                }}
                                            />
                                        </Label>
                                    )}
                                {currentTask?.status === 'Completed' && (
                                    <Combobox
                                        id="task-completed-by"
                                        label="Completed by"
                                        labelClassName="w-full"
                                        options={
                                            crewMembers
                                                ? crewMembers?.map(
                                                      (member: any) => ({
                                                          value: member.id,
                                                          label: `${member.firstName ?? ''} ${member.surname ?? ''}`,
                                                          crew: member,
                                                          profile: {
                                                              firstName:
                                                                  member.firstName,
                                                              surname:
                                                                  member.surname,
                                                              avatar: null,
                                                          },
                                                      }),
                                                  )
                                                : []
                                        }
                                        isLoading={
                                            !crewMembers || !maintenanceChecks
                                        }
                                        defaultValues={
                                            maintenanceChecks?.completedBy
                                                ? {
                                                      label: `${maintenanceChecks.completedBy.firstName ?? ''} ${maintenanceChecks.completedBy.surname ?? ''}`,
                                                      value: maintenanceChecks
                                                          .completedBy.id,
                                                  }
                                                : null
                                        }
                                        onChange={(value: any) => {
                                            if (value) {
                                                currentTask.completedBy =
                                                    value.value
                                            }
                                        }}
                                        placeholder="Select team"
                                    />
                                )}
                            </div>

                            {vesselStatus && (
                                <CheckFieldLabel
                                    id="vesselStatus-onChangeComplete"
                                    checked={
                                        vesselStatus?.status === 'OutOfService'
                                    }
                                    onCheckedChange={(checked) => {
                                        handleSetVesselStatus(checked === true)
                                    }}
                                    disabled={
                                        vesselStatus?.status === 'OnVoyage'
                                    }
                                    className={`w-full ${vesselStatus?.status === 'OnVoyage' ? 'opacity-60' : ''}`}>
                                    <div className="flex flex-col gap-1">
                                        <span className="font-medium">
                                            {`Mark vessel out of service${
                                                vesselStatus?.status
                                                    ? ' - ' +
                                                      vesselStatus.status
                                                    : ''
                                            }`}
                                        </span>
                                        <div
                                            className={`text-sm ${vesselStatus?.status === 'OnVoyage' ? 'text-cinnabar-600' : 'text-muted-foreground'}`}>
                                            {vesselStatus?.status === 'OnVoyage'
                                                ? "This vessel is on a voyage and can't be set to out of service"
                                                : 'Until this task is complete the related vessel cannot be used normally'}
                                        </div>
                                    </div>
                                </CheckFieldLabel>
                            )}
                        </CardContent>
                    </Card>
                </>
            )}

            {taskTab === 'completed' && (
                <Card className="mx-2.5">
                    <CardHeader>
                        <H4>Completed Recurring Tasks</H4>
                        <P>View the history of completed recurring tasks.</P>
                    </CardHeader>
                    <CardContent>
                        {completedRecurringTasks.length > 0 ? (
                            <DataTable
                                columns={createColumns([
                                    {
                                        accessorKey: 'title',
                                        header: 'Task',
                                        cellAlignment: 'left',
                                        cell: ({ row }: { row: any }) => {
                                            const maintenanceCheck =
                                                row.original
                                            return (
                                                <div className="px-2 py-1">
                                                    <div className="flex items-center justify-between">
                                                        <span className="text-foreground flex items-center">
                                                            <Link
                                                                href={`/maintenance?taskID=${maintenanceCheck.id}&redirect_to=${pathname}?${searchParams.toString()}`}
                                                                className="focus:outline-none hover:underline">
                                                                {
                                                                    maintenanceCheck.name
                                                                }
                                                            </Link>
                                                            {maintenanceCheck
                                                                ?.isOverDue
                                                                ?.status ===
                                                                'High' && (
                                                                <div className="inline-block rounded px-3 py-1 ml-3 text-destructive bg-slred-100 border border-destructive">
                                                                    {
                                                                        maintenanceCheck
                                                                            ?.isOverDue
                                                                            ?.days
                                                                    }
                                                                </div>
                                                            )}
                                                            {(maintenanceCheck
                                                                ?.isOverDue
                                                                ?.status ===
                                                                'Medium' ||
                                                                maintenanceCheck
                                                                    ?.isOverDue
                                                                    ?.days ===
                                                                    'Save As Draft') && (
                                                                <div className="inline-block rounded px-3 py-1 ml-3 text-yellow-600 bg-yellow-100 border border-yellow-600">
                                                                    {
                                                                        maintenanceCheck
                                                                            ?.isOverDue
                                                                            ?.days
                                                                    }
                                                                </div>
                                                            )}
                                                            {(maintenanceCheck
                                                                ?.isOverDue
                                                                ?.status ===
                                                                'Low' ||
                                                                maintenanceCheck
                                                                    ?.isOverDue
                                                                    ?.status ===
                                                                    'Upcoming' ||
                                                                maintenanceCheck
                                                                    ?.isOverDue
                                                                    ?.status ===
                                                                    'Completed') &&
                                                                maintenanceCheck
                                                                    ?.isOverDue
                                                                    ?.days !==
                                                                    'Save As Draft' && (
                                                                    <div className="inline-block ml-3">
                                                                        {maintenanceCheck
                                                                            ?.isOverDue
                                                                            ?.days ||
                                                                            maintenanceCheck
                                                                                ?.isOverDue
                                                                                ?.status}
                                                                    </div>
                                                                )}
                                                        </span>
                                                        <div className="flex items-center">
                                                            {maintenanceCheck.basicComponentID !==
                                                                null &&
                                                                vessels
                                                                    ?.filter(
                                                                        (
                                                                            vessel: any,
                                                                        ) =>
                                                                            vessel?.id ==
                                                                            maintenanceCheck.basicComponentID,
                                                                    )
                                                                    .map(
                                                                        (
                                                                            vessel: any,
                                                                        ) => (
                                                                            <div
                                                                                key={
                                                                                    vessel.id
                                                                                }
                                                                                className="inline-block rounded px-3 py-1 ml-3">
                                                                                <span>
                                                                                    {
                                                                                        vessel.title
                                                                                    }
                                                                                </span>
                                                                            </div>
                                                                        ),
                                                                    )}
                                                            {maintenanceCheck.comments !==
                                                                null && (
                                                                <div className="w-14 flex items-center pl-1">
                                                                    <Popover>
                                                                        <PopoverTrigger
                                                                            asChild>
                                                                            <Button
                                                                                variant={
                                                                                    'ghost'
                                                                                }
                                                                                size={
                                                                                    'icon'
                                                                                }>
                                                                                <MessageSquareText className="text-slgreen-1000" />
                                                                            </Button>
                                                                        </PopoverTrigger>
                                                                        <PopoverContent>
                                                                            {
                                                                                maintenanceCheck.comments
                                                                            }
                                                                        </PopoverContent>
                                                                    </Popover>
                                                                </div>
                                                            )}
                                                        </div>
                                                    </div>
                                                    {maintenanceCheck.description && (
                                                        <div className="mt-1 text-sm text-muted-foreground">
                                                            {
                                                                maintenanceCheck.description
                                                            }
                                                        </div>
                                                    )}
                                                </div>
                                            )
                                        },
                                    },
                                    {
                                        accessorKey: 'assignedTo',
                                        header: 'Assigned To',
                                        cellAlignment: 'center',
                                        cell: ({ row }: { row: any }) => {
                                            const maintenanceCheck =
                                                row.original
                                            const assignedCrew = crewInfo?.find(
                                                (crew: any) =>
                                                    crew.id ===
                                                    maintenanceCheck.assignedToID,
                                            )
                                            return assignedCrew ? (
                                                <div className="flex items-center gap-2.5 justify-center">
                                                    <Avatar className="h-8 w-8">
                                                        <AvatarFallback className="text-xs">
                                                            {getCrewInitials(
                                                                assignedCrew.firstName,
                                                                assignedCrew.surname,
                                                            )}
                                                        </AvatarFallback>
                                                    </Avatar>
                                                    <Link
                                                        href={`/crew/info?id=${assignedCrew.id}`}
                                                        className="hover:underline">
                                                        {assignedCrew.firstName}{' '}
                                                        {assignedCrew.surname}
                                                    </Link>
                                                </div>
                                            ) : (
                                                <span>-</span>
                                            )
                                        },
                                    },
                                    {
                                        accessorKey: 'status',
                                        header: 'Due Date',
                                        cellAlignment: 'right',
                                        cell: ({ row }: { row: any }) => {
                                            const maintenanceCheck =
                                                row.original
                                            const isOverdue =
                                                maintenanceCheck?.isOverDue
                                                    ?.status === 'High'

                                            return (
                                                <div className="text-right px-2 py-1">
                                                    <div className="text-sm text-muted-foreground mb-1">
                                                        {formatDate(
                                                            maintenanceCheck.expires,
                                                        )}
                                                    </div>
                                                    <div
                                                        className={
                                                            isOverdue &&
                                                            maintenanceCheck.status !==
                                                                'Completed'
                                                                ? 'inline-block rounded px-3 py-1 bg-destructive-foreground border border-destructive text-cinnabar-700'
                                                                : 'inline-block'
                                                        }>
                                                        <span>
                                                            {maintenanceCheck.status.replaceAll(
                                                                '_',
                                                                ' ',
                                                            )}
                                                        </span>
                                                    </div>
                                                </div>
                                            )
                                        },
                                    },
                                ])}
                                data={completedRecurringTasks}
                                showToolbar={false}
                                pageSize={10}
                            />
                        ) : (
                            <div className="flex justify-between items-center gap-2 p-4">
                                <p className="text-muted-foreground">
                                    No completed recurring tasks found.
                                </p>
                            </div>
                        )}
                    </CardContent>
                </Card>
            )}

            {/* Action Footer */}
            {inSidebar ? (
                <div className="flex justify-end gap-3 mt-4 mx-2.5">
                    {isDesktop && (
                        <Button
                            variant="back"
                            iconLeft={<ArrowLeft />}
                            onClick={handleCancel}>
                            Cancel
                        </Button>
                    )}
                    <Button
                        variant="destructive"
                        onClick={() => {
                            if (
                                !delete_task ||
                                (displayRecurringTasks && !edit_recurring_task)
                            ) {
                                toast({
                                    title: 'Error',
                                    description:
                                        'You do not have permission to delete this task',
                                    variant: 'destructive',
                                })
                                return
                            }
                            setOpenDeleteTaskDialog(true)
                        }}>
                        {responsiveLabel('Delete', 'Archive task')}
                    </Button>
                    <Button
                        variant="primary"
                        iconLeft={<Check />}
                        disabled={saveDisabled}
                        onClick={handleUpdateTask}>
                        {responsiveLabel('Update', 'Update task')}
                    </Button>
                </div>
            ) : (
                <ActionFooter
                    showFooter={true}
                    showCreateTask={false}
                    saveText={responsiveLabel('Update', 'Update task')}
                    saveIcon="check"
                    saveDisabled={saveDisabled}
                    onCancel={isDesktop ? undefined : handleCancel}
                    onSave={handleUpdateTask}
                    noBorder={true}
                    className="gap-3">
                    {isDesktop && (
                        <Button
                            variant="back"
                            iconLeft={<ArrowLeft />}
                            onClick={handleCancel}>
                            Cancel
                        </Button>
                    )}
                    <Button
                        variant="destructive"
                        onClick={() => {
                            if (
                                !delete_task ||
                                (displayRecurringTasks && !edit_recurring_task)
                            ) {
                                toast({
                                    title: 'Error',
                                    description:
                                        'You do not have permission to delete this task',
                                    variant: 'destructive',
                                })
                                return
                            }
                            setOpenDeleteTaskDialog(true)
                        }}>
                        {responsiveLabel('Delete', 'Archive task')}
                    </Button>
                    <Button
                        variant="primary"
                        disabled={saveDisabled}
                        onClick={handleUpdateTask}>
                        {responsiveLabel('Update', 'Update task')}
                    </Button>
                </ActionFooter>
            )}

            <AlertDialogNew
                openDialog={openSubTaskDialog}
                setOpenDialog={setOpenSubTaskDialog}
                handleCreate={handleCreateSubTask}
                size="xl"
                actionText="Create sub-task"
                title="Create new sub-task">
                <AlertDialogBody className="space-y-4">
                    <Input
                        id={`subtask-name`}
                        type="text"
                        className={''}
                        placeholder="Sub-task"
                    />

                    <Combobox
                        id="subtask-inventory"
                        label="Inventory item (optional)"
                        options={
                            inventories && inventories.length > 0
                                ? filterInventories(inventories)
                                      ?.filter(
                                          (i: any) =>
                                              i.vesselID ==
                                                  maintenanceChecks
                                                      ?.basicComponent?.id ||
                                              i.vesselID ==
                                                  currentTask?.basicComponentID,
                                      )
                                      ?.map((inventory: any) => ({
                                          value: inventory.id,
                                          label: inventory.item,
                                      }))
                                : []
                        }
                        isLoading={!inventories || inventories.length === 0}
                        onChange={handleSubtaskOnChangeInventory}
                        placeholder="Select inventory item"
                    />

                    <Editor
                        id="comment"
                        placeholder="Comment"
                        className={''}
                        content={subtaskContent}
                        handleEditorChange={handleSubtaskEditorChange}
                    />
                </AlertDialogBody>
            </AlertDialogNew>
            <AlertDialogNew
                openDialog={displayUpdateSubTask}
                setOpenDialog={setDisplayUpdateSubTask}
                title="Sub-task details"
                handleCancel={() => setDisplayUpdateSubTask(false)}
                handleCreate={() => handleUpdateSubTask('updateSubTask')}
                handleDestructiveAction={() =>
                    handleUpdateSubTask('deleteSubTask')
                }
                showDestructiveAction={true}
                actionText="Update"
                destructiveActionText="Delete"
                cancelText="Cancel"
                size="lg">
                <div className="space-y-4">
                    <Label label="Sub-task name" htmlFor="subtask-name">
                        <Input
                            id="subtask-name"
                            type="text"
                            defaultValue={
                                subTasks.filter(
                                    (subtask: any) =>
                                        subtask.id === currentSubTaskCheckID,
                                )[0]?.maintenanceScheduleSubTask.task
                            }
                            onChange={(e: any) =>
                                setSubTask(
                                    subTasks.map((subtask: any) =>
                                        subtask.id === currentSubTaskCheckID
                                            ? {
                                                  ...subtask,
                                                  maintenanceScheduleSubTask: {
                                                      ...subtask.maintenanceScheduleSubTask,
                                                      task: e.target.value,
                                                  },
                                              }
                                            : subtask,
                                    ),
                                )
                            }
                            placeholder={`Sub-task ${currentSubTaskCheckID}`}
                        />
                    </Label>

                    <Combobox
                        id="subtask-inventory"
                        label="Inventory item (optional)"
                        options={
                            inventories && inventories.length > 0
                                ? filterInventories(inventories)
                                      ?.filter(
                                          (i: any) =>
                                              i.vesselID ==
                                                  maintenanceChecks
                                                      ?.basicComponent?.id ||
                                              i.vesselID ==
                                                  currentTask?.basicComponentID,
                                      )
                                      ?.map((inventory: any) => ({
                                          value: inventory.id,
                                          label: inventory.item,
                                      }))
                                : []
                        }
                        isLoading={!inventories || inventories.length === 0}
                        value={
                            inventories?.filter(
                                (i: any) => i.id == subtaskInventoryValue,
                            )?.length > 0
                                ? {
                                      value: inventories.find(
                                          (i: any) =>
                                              i.id == subtaskInventoryValue,
                                      ).id,
                                      label: inventories.find(
                                          (i: any) =>
                                              i.id == subtaskInventoryValue,
                                      ).item,
                                  }
                                : undefined
                        }
                        onChange={handleSubtaskOnChangeInventory}
                        placeholder="Select inventory item"
                    />

                    {taskRecords &&
                        taskRecords.length > 0 &&
                        taskRecords.filter(
                            (record: any) =>
                                record.subTaskID == currentSubTaskCheckID,
                        ).length > 0 && (
                            <ScrollArea className="mb-4 rounded-md bg-card border border-border border-dashed">
                                <div className="space-y-2.5 p-2 max-h-[300px]">
                                    {taskRecords
                                        .filter(
                                            (record: any) =>
                                                record.subTaskID ==
                                                currentSubTaskCheckID,
                                        )
                                        .map((record: any) => (
                                            <RecordCard
                                                key={`${record.id}-record`}
                                                record={record}
                                                onEdit={handleEditRecord}
                                                onDelete={(recordId) => {
                                                    setDeleteRecordID(recordId)
                                                    setOpenDeleteRecordDialog(
                                                        true,
                                                    )
                                                }}
                                            />
                                        ))}
                                </div>
                            </ScrollArea>
                        )}

                    <Button variant="outline" onClick={handleAddRecord}>
                        Add Record
                    </Button>

                    <UploadCloudFlare
                        files={subTaskAttachments}
                        setFiles={setSubTaskAttachments}
                    />

                    <Label label="Comment" htmlFor="comment">
                        <Textarea
                            id="comment"
                            placeholder="Comment"
                            rows={8}
                            value={stripHtmlTags(subtaskContent)}
                            onChange={(e) =>
                                handleSubtaskEditorChange(e.target.value)
                            }
                        />
                    </Label>
                </div>

                <Separator className="my-6" />

                <CheckFieldLabel
                    id="task-alertChange"
                    checked={alertSubTaskStatus}
                    onCheckedChange={() =>
                        setAlertSubTaskStatus(!alertSubTaskStatus)
                    }
                    label={alertSubTaskStatus ? 'Completed' : 'Complete'}
                />
            </AlertDialogNew>
            <AlertDialogNew
                openDialog={displayAddFindings}
                setOpenDialog={setDisplayAddFindings}
                handleCreate={() => handleUpdateSubTask('updateFindings')}
                actionText="Complete SubTask"
                title="Add Findings">
                <AlertDialogBody className="space-y-4">
                    <Textarea
                        id={`subtask-findings`}
                        rows={4}
                        className={'w-full'}
                        placeholder="Findings"
                        defaultValue={stripHtmlTags(
                            subTasks.filter(
                                (subtask: any) =>
                                    subtask.id === currentSubTaskCheckID,
                            )[0]?.findings,
                        )}
                    />

                    <Combobox
                        id="comment-author"
                        label="Crew member"
                        options={members || []}
                        isLoading={!members || members.length === 0}
                        placeholder="Crew member"
                        className={'w-full'}
                        value={
                            members?.find(
                                (member: any) => member.value == authorID,
                            ) || undefined
                        }
                        onChange={(value: any) => setAuthorID(value?.value)}
                    />

                    <DatePicker
                        mode="single"
                        type="datetime"
                        label="Select completed date"
                        value={scheduleCompletedDate}
                        onChange={handleScheduleCompletedDateChange}
                        clearable={true}
                        confirmSelection={true}
                    />
                </AlertDialogBody>
            </AlertDialogNew>
            <AlertDialogNew
                openDialog={openDeleteTaskDialog}
                setOpenDialog={setOpenDeleteTaskDialog}
                handleCreate={handleDeleteCheck}
                actionText="Archive Task"
                title="Archive Task"
                variant="danger"
                showDestructiveAction={true}
                handleDestructiveAction={handleDeleteCheck}>
                <P>Are you sure you want to Archive this task?</P>
            </AlertDialogNew>
            {openRecordsDialog && (
                <AlertDialogNew
                    openDialog={openRecordsDialog}
                    setOpenDialog={setOpenRecordsDialog}
                    handleCreate={handleSaveRecords}
                    title={
                        commentData?.id > 0 ? 'Update note' : 'Create new note'
                    }
                    actionText={
                        commentData?.id > 0
                            ? 'Update'
                            : responsiveLabel('Create', 'Create note')
                    }>
                    <AlertDialogBody className="space-y-4">
                        <DatePicker
                            mode="single"
                            type="datetime"
                            label="Time of completion"
                            value={commentTime}
                            onChange={handleCommentTimeChange}
                            clearable={true}
                            modal
                            confirmSelection={true}
                        />

                        <Combobox
                            id="comment-author"
                            label="Crew member"
                            options={members || []}
                            isLoading={!members}
                            buttonClassName={'w-full'}
                            placeholder="Crew member"
                            value={
                                members?.find(
                                    (member: any) =>
                                        member.value == commentData?.author?.id,
                                ) || undefined
                            }
                            onChange={(value: any) =>
                                setCommentData({
                                    ...commentData,
                                    authorID: value?.value,
                                })
                            }
                        />

                        <Label label="Comment" htmlFor="comment">
                            <Textarea
                                id="comment"
                                placeholder="Comment"
                                rows={8}
                                value={stripHtmlTags(reviewContent || '')}
                                onChange={(e) =>
                                    handleReviewEditorChange(e.target.value)
                                }
                            />
                        </Label>
                    </AlertDialogBody>
                </AlertDialogNew>
            )}

            <AlertDialogNew
                openDialog={openDeleteRecordDialog}
                setOpenDialog={setOpenDeleteRecordDialog}
                title="Delete Record"
                variant="warning"
                showDestructiveAction={true}
                handleDestructiveAction={handleDeleteRecord}>
                <P>Are you sure you want to delete this record?</P>
            </AlertDialogNew>

            <AlertDialogNew
                openDialog={createCategoryDialog}
                setOpenDialog={setCreateCategoryDialog}
                handleCreate={handleCreateCategory}
                actionText="Create Category"
                title="Create new category">
                <Input
                    id={`task-new-category`}
                    name={`task-new-category`}
                    type="text"
                    className={'w-full'}
                    placeholder="Category"
                />
            </AlertDialogNew>
            <AlertDialogNew
                openDialog={displayEditStatus}
                setOpenDialog={setDisplayEditStatus}
                handleCreate={handleUpdateVesselStatus}
                actionText="Update"
                title="Update Vessel Status">
                <AlertDialogBody className="space-y-4">
                    <DatePicker
                        mode="single"
                        type="date"
                        label="Date"
                        value={
                            vesselStatus?.date
                                ? dayjs(vesselStatus.date).toDate()
                                : undefined
                        }
                        onChange={handleVesselStatusDate}
                        placeholder="Select date"
                        clearable={true}
                    />
                    <Combobox
                        id="vessel-status"
                        options={
                            vesselStatuses && vesselStatuses.length > 0
                                ? vesselStatuses
                                : []
                        }
                        placeholder="Status"
                        isLoading={
                            !vesselStatuses || vesselStatuses.length === 0
                        }
                        value={
                            vesselStatuses && vesselStatuses.length > 0
                                ? vesselStatuses.find(
                                      (status: any) =>
                                          vesselStatus?.status === status.value,
                                  ) || undefined
                                : undefined
                        }
                        onChange={handleVesselStatusChange}
                    />
                    {vesselStatus?.status === 'OutOfService' && (
                        <Combobox
                            id="vessel-status-reason"
                            options={
                                vesselStatusReason &&
                                vesselStatusReason.length > 0
                                    ? vesselStatusReason
                                    : []
                            }
                            placeholder="Reason"
                            isLoading={
                                !vesselStatusReason ||
                                vesselStatusReason.length === 0
                            }
                            value={
                                vesselStatusReason &&
                                vesselStatusReason.length > 0
                                    ? vesselStatusReason.find(
                                          (status: any) =>
                                              vesselStatus?.reason ===
                                              status.value,
                                      ) || undefined
                                    : undefined
                            }
                            onChange={handleVesselStatusReasonChange}
                        />
                    )}
                    {vesselStatus?.status === 'OutOfService' &&
                        vesselStatus?.reason === 'Other' && (
                            <Textarea
                                id="vessel-status-other"
                                className="w-full"
                                placeholder="Other description"
                                value={stripHtmlTags(vesselStatus?.otherReason)}
                                onChange={(e) =>
                                    setVesselStatus({
                                        ...vesselStatus,
                                        otherReason: e.target.value,
                                    })
                                }
                            />
                        )}
                    {vesselStatus?.status === 'OutOfService' && (
                        <Label label="Comment" htmlFor="vessel-status-comment">
                            <Textarea
                                id="vessel-status-comment"
                                placeholder="Comment"
                                rows={6}
                                value={stripHtmlTags(
                                    vesselStatus?.comment || '',
                                )}
                                onChange={(e) =>
                                    setVesselStatus({
                                        ...vesselStatus,
                                        comment: e.target.value,
                                    })
                                }
                            />
                        </Label>
                    )}
                    {vesselStatus?.status === 'OutOfService' && (
                        <DatePicker
                            mode="single"
                            type="date"
                            label="Expected return date"
                            value={
                                vesselStatus?.expectedReturn
                                    ? (() => {
                                          const date = dayjs(
                                              vesselStatus.expectedReturn,
                                          )
                                          return typeof date.toDate ===
                                              'function'
                                              ? date.toDate()
                                              : new Date(date.toString())
                                      })()
                                    : undefined
                            }
                            onChange={handleVesselStatusReturnDate}
                            placeholder="Expected return date"
                            clearable={true}
                        />
                    )}
                </AlertDialogBody>
            </AlertDialogNew>
            {/* Subtask Findings Dialog */}
            <AlertDialogNew
                openDialog={openSubTaskFindingsDialog}
                setOpenDialog={setOpenSubTaskFindingsDialog}
                title="Task Findings"
                description={
                    selectedSubtaskFindings
                        ? `Details for ${selectedSubtaskFindings.maintenanceScheduleSubTask.task}`
                        : ''
                }
                cancelText="Close"
                noButton={true}>
                <div className="mt-2 p-4 bg-muted/50 rounded-lg">
                    {selectedSubtaskFindings?.findings}
                </div>
            </AlertDialogNew>
        </div>
    )
}
