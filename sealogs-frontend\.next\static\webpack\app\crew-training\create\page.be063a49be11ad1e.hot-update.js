"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/crew-training/create/page",{

/***/ "(app-pages-browser)/./src/app/ui/crew/multiselect-dropdown/multiselect-dropdown.tsx":
/*!***********************************************************************!*\
  !*** ./src/app/ui/crew/multiselect-dropdown/multiselect-dropdown.tsx ***!
  \***********************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var lodash_isEmpty__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! lodash/isEmpty */ \"(app-pages-browser)/./node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/isEmpty.js\");\n/* harmony import */ var lodash_isEmpty__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(lodash_isEmpty__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/app/lib/graphQL/mutation */ \"(app-pages-browser)/./src/app/lib/graphQL/mutation/index.ts\");\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @apollo/client */ \"(app-pages-browser)/./node_modules/.pnpm/@apollo+client@3.13.8_@type_3a2c516023e671829487810ec4a9ea8f/node_modules/@apollo/client/react/hooks/useLazyQuery.js\");\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @apollo/client */ \"(app-pages-browser)/./node_modules/.pnpm/@apollo+client@3.13.8_@type_3a2c516023e671829487810ec4a9ea8f/node_modules/@apollo/client/react/hooks/useMutation.js\");\n/* harmony import */ var _app_offline_models_seaLogsMember__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/app/offline/models/seaLogsMember */ \"(app-pages-browser)/./src/app/offline/models/seaLogsMember.js\");\n/* harmony import */ var _app_offline_helpers_functions__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/app/offline/helpers/functions */ \"(app-pages-browser)/./src/app/offline/helpers/functions.ts\");\n/* harmony import */ var _components_ui_comboBox__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/comboBox */ \"(app-pages-browser)/./src/components/ui/comboBox.tsx\");\n/* harmony import */ var _add_crew_member_dialog__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../add-crew-member-dialog */ \"(app-pages-browser)/./src/app/ui/crew/add-crew-member-dialog.tsx\");\n/* harmony import */ var _queries__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./queries */ \"(app-pages-browser)/./src/app/ui/crew/multiselect-dropdown/queries.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nconst CrewMultiSelectDropdown = (param)=>{\n    let { value = [], onChange, memberIdOptions = [], departments = [], filterByAdmin = false, offline = false, vesselID = 0 } = param;\n    _s();\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [crewList, setCrewList] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [openCreateMemberDialog, setOpenCreateMemberDialog] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedIDs, setSelectedIDs] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const seaLogsMemberModel = new _app_offline_models_seaLogsMember__WEBPACK_IMPORTED_MODULE_4__[\"default\"]();\n    const handleSetCrewList = (crewListRaw)=>{\n        console.log(\"\\uD83D\\uDD27 CrewMultiSelectDropdown - handleSetCrewList called with:\", {\n            crewListRawLength: crewListRaw === null || crewListRaw === void 0 ? void 0 : crewListRaw.length,\n            vesselID,\n            departments: departments.length,\n            memberIdOptions: memberIdOptions.length\n        });\n        // If vesselID > 0, filter the crew list to display only the vessel crew.\n        const vesselCrewList = vesselID > 0 ? crewListRaw.filter((crew)=>crew.vehicles.nodes.some((vehicle)=>+vehicle.id === vesselID)) : crewListRaw;\n        console.log(\"\\uD83D\\uDD27 CrewMultiSelectDropdown - vesselCrewList filtered:\", {\n            originalLength: crewListRaw === null || crewListRaw === void 0 ? void 0 : crewListRaw.length,\n            filteredLength: vesselCrewList === null || vesselCrewList === void 0 ? void 0 : vesselCrewList.length\n        });\n        const createOption = {\n            value: \"newCrewMember\",\n            label: \"--- Create Crew Member ---\"\n        };\n        const data = vesselCrewList.filter((crew)=>filterByAdmin ? !crewIsAdmin(crew) : true);\n        if (departments.length > 0) {\n            const departmentList = departments.flatMap((department)=>{\n                return department.id;\n            });\n            const crews = data.filter((crew)=>crew.departments.nodes.some((node)=>departmentList.includes(node.id))).map((item)=>{\n                var _item_firstName, _item_surname;\n                return {\n                    value: item.id,\n                    label: \"\".concat((_item_firstName = item.firstName) !== null && _item_firstName !== void 0 ? _item_firstName : \"\", \" \").concat((_item_surname = item.surname) !== null && _item_surname !== void 0 ? _item_surname : \"\"),\n                    profile: {\n                        firstName: item.firstName,\n                        surname: item.surname,\n                        avatar: null\n                    }\n                };\n            });\n            if (memberIdOptions.length === 0) {\n                setCrewList([\n                    createOption,\n                    ...crews\n                ]);\n            } else {\n                const filteredCrewList = crews.filter((crew)=>{\n                    return memberIdOptions.includes(crew.value);\n                });\n                setCrewList(filteredCrewList);\n            }\n        } else {\n            const crews = data.map((item)=>{\n                var _item_firstName, _item_surname;\n                return {\n                    value: item.id,\n                    label: \"\".concat((_item_firstName = item.firstName) !== null && _item_firstName !== void 0 ? _item_firstName : \"\", \" \").concat((_item_surname = item.surname) !== null && _item_surname !== void 0 ? _item_surname : \"\"),\n                    profile: {\n                        firstName: item.firstName,\n                        surname: item.surname,\n                        avatar: null\n                    }\n                };\n            });\n            if (memberIdOptions.length === 0) {\n                setCrewList([\n                    createOption,\n                    ...crews\n                ]);\n            } else {\n                const filteredCrewList = crews.filter((crew)=>{\n                    return memberIdOptions.includes(crew.value);\n                });\n                setCrewList(filteredCrewList);\n            }\n        }\n        console.log(\"\\uD83D\\uDD27 CrewMultiSelectDropdown - final crewList set:\", {\n            crewListLength: crewList.length,\n            sampleCrew: crewList[0]\n        });\n    };\n    const [querySeaLogsMembersList] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_9__.useLazyQuery)(_queries__WEBPACK_IMPORTED_MODULE_8__.ReadSeaLogsMembers, {\n        fetchPolicy: \"cache-and-network\",\n        onError: (error)=>{\n            console.error(\"querySeaLogsMembersList error\", error);\n        }\n    });\n    const loadCrewMembers = async ()=>{\n        console.log(\"\\uD83D\\uDD27 CrewMultiSelectDropdown - loadCrewMembers started\");\n        let allMembers = [];\n        let offset = 0;\n        const limit = 100;\n        let hasNextPage = true;\n        try {\n            while(hasNextPage){\n                var _response_data;\n                console.log(\"\\uD83D\\uDD27 CrewMultiSelectDropdown - fetching page: offset=\".concat(offset, \", limit=\").concat(limit));\n                const response = await querySeaLogsMembersList({\n                    variables: {\n                        filter: {\n                            isArchived: {\n                                eq: false\n                            }\n                        },\n                        limit: limit,\n                        offset: offset\n                    }\n                });\n                if ((_response_data = response.data) === null || _response_data === void 0 ? void 0 : _response_data.readSeaLogsMembers) {\n                    const data = response.data.readSeaLogsMembers.nodes;\n                    const pageInfo = response.data.readSeaLogsMembers.pageInfo;\n                    console.log(\"\\uD83D\\uDD27 CrewMultiSelectDropdown - page data:\", {\n                        dataLength: data === null || data === void 0 ? void 0 : data.length,\n                        hasNextPage: pageInfo === null || pageInfo === void 0 ? void 0 : pageInfo.hasNextPage\n                    });\n                    if (data && data.length > 0) {\n                        allMembers = [\n                            ...allMembers,\n                            ...data\n                        ];\n                    }\n                    hasNextPage = (pageInfo === null || pageInfo === void 0 ? void 0 : pageInfo.hasNextPage) || false;\n                    offset += limit;\n                } else {\n                    hasNextPage = false;\n                }\n            }\n            console.log(\"\\uD83D\\uDD27 CrewMultiSelectDropdown - loadCrewMembers completed:\", {\n                totalMembers: allMembers.length\n            });\n            // Set all collected members at once\n            if (allMembers.length > 0) {\n                handleSetCrewList(allMembers);\n            }\n        } catch (error) {\n            console.error(\"\\uD83D\\uDD27 CrewMultiSelectDropdown - Error loading all crew members:\", error);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (isLoading && !offline) {\n            loadCrewMembers();\n            setIsLoading(false);\n        }\n    }, [\n        isLoading,\n        offline\n    ]);\n    // if (!offline) {\n    // getSeaLogsMembersList(handleSetCrewList)\n    // }\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (offline) {\n            seaLogsMemberModel.getAll().then((data)=>{\n                handleSetCrewList(data);\n            });\n        }\n    }, [\n        offline\n    ]);\n    const crewIsAdmin = (crew)=>{\n        var _crew_groups_nodes;\n        return ((_crew_groups_nodes = crew.groups.nodes) === null || _crew_groups_nodes === void 0 ? void 0 : _crew_groups_nodes.filter((permission)=>{\n            return permission.code === \"admin\";\n        }).length) > 0;\n    };\n    const handleOnChange = (value)=>{\n        console.log(\"\\uD83D\\uDD27 CrewMultiSelectDropdown - handleOnChange called with:\", {\n            value,\n            valueType: typeof value,\n            isArray: Array.isArray(value)\n        });\n        if (!value) {\n            console.log(\"\\uD83D\\uDD27 CrewMultiSelectDropdown - clearing selection (no value)\");\n            setSelectedIDs([]);\n            onChange([]);\n            return;\n        }\n        const valueArray = Array.isArray(value) ? value : [\n            value\n        ];\n        console.log(\"\\uD83D\\uDD27 CrewMultiSelectDropdown - valueArray:\", valueArray);\n        // Fix the condition to properly check for 'newCrewMember'\n        if (valueArray.find((option)=>option.value === \"newCrewMember\")) {\n            console.log(\"\\uD83D\\uDD27 CrewMultiSelectDropdown - opening create member dialog\");\n            setOpenCreateMemberDialog(true);\n            return;\n        }\n        if (valueArray.length === 0) {\n            console.log(\"\\uD83D\\uDD27 CrewMultiSelectDropdown - clearing selection (empty array)\");\n            setSelectedIDs([]);\n            onChange([]);\n            return;\n        }\n        // Ensure we're working with valid Option objects that have both value and label\n        const validOptions = valueArray.filter((option)=>option && typeof option === \"object\" && option.value !== undefined && option.label !== null && option.label !== undefined);\n        console.log(\"\\uD83D\\uDD27 CrewMultiSelectDropdown - validOptions filtered:\", {\n            originalLength: valueArray.length,\n            validLength: validOptions.length,\n            validOptions\n        });\n        setSelectedIDs(validOptions);\n        onChange(validOptions);\n    };\n    const [queryAddMember] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_10__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_3__.CREATE_USER, {\n        fetchPolicy: \"no-cache\",\n        onCompleted: (response)=>{\n            const data = response.createSeaLogsMember;\n            if (data.id > 0) {\n                setOpenCreateMemberDialog(false);\n                const newData = {\n                    value: data.id,\n                    label: data.firstName + \" \" + data.surname\n                };\n                setCrewList([\n                    ...crewList,\n                    newData\n                ]);\n                const newSelectedIDs = [\n                    ...selectedIDs,\n                    newData\n                ];\n                setSelectedIDs(newSelectedIDs);\n                onChange(newSelectedIDs);\n                setError(false);\n            }\n        },\n        onError: (error)=>{\n            console.error(\"createUser error\", error.message);\n            setError(error);\n        }\n    });\n    const handleAddNewMember = async ()=>{\n        const variables = {\n            input: {\n                firstName: document.getElementById(\"crew-firstName\").value ? document.getElementById(\"crew-firstName\").value : null,\n                surname: document.getElementById(\"crew-surname\").value ? document.getElementById(\"crew-surname\").value : null,\n                email: document.getElementById(\"crew-email\").value ? document.getElementById(\"crew-email\").value : null,\n                phoneNumber: document.getElementById(\"crew-phoneNumber\").value ? document.getElementById(\"crew-phoneNumber\").value : null,\n                username: document.getElementById(\"crew-username\").value ? document.getElementById(\"crew-username\").value : null,\n                password: document.getElementById(\"crew-password\").value ? document.getElementById(\"crew-password\").value : null\n            }\n        };\n        if (offline) {\n            // queryAddMember\n            const data = await seaLogsMemberModel.save({\n                ...variables.input,\n                id: (0,_app_offline_helpers_functions__WEBPACK_IMPORTED_MODULE_5__.generateUniqueId)()\n            });\n            setOpenCreateMemberDialog(false);\n            const newData = {\n                value: data.id,\n                label: data.firstName + \" \" + data.surname\n            };\n            setCrewList([\n                ...crewList,\n                newData\n            ]);\n            const newSelectedIDs = [\n                ...selectedIDs,\n                newData\n            ];\n            setSelectedIDs(newSelectedIDs);\n            onChange(newSelectedIDs);\n            setError(false);\n        } else {\n            await queryAddMember({\n                variables: variables\n            });\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        console.log(\"\\uD83D\\uDD27 CrewMultiSelectDropdown - useEffect [value, crewList] triggered:\", {\n            valueLength: value === null || value === void 0 ? void 0 : value.length,\n            value,\n            crewListLength: crewList === null || crewList === void 0 ? void 0 : crewList.length,\n            crewListEmpty: lodash_isEmpty__WEBPACK_IMPORTED_MODULE_2___default()(crewList),\n            valueEmpty: lodash_isEmpty__WEBPACK_IMPORTED_MODULE_2___default()(value)\n        });\n        if (!lodash_isEmpty__WEBPACK_IMPORTED_MODULE_2___default()(crewList) && !lodash_isEmpty__WEBPACK_IMPORTED_MODULE_2___default()(value)) {\n            console.log(\"\\uD83D\\uDD27 CrewMultiSelectDropdown - mapping values to options\");\n            // Convert value array to proper Option objects\n            const selectedOptions = value.map((id)=>{\n                const option = crewList.find((crew)=>crew.value === id);\n                console.log(\"\\uD83D\\uDD27 CrewMultiSelectDropdown - mapping ID \".concat(id, \":\"), {\n                    found: !!option,\n                    option\n                });\n                return option;\n            }).filter(Boolean) // Remove any undefined/null options\n            ;\n            console.log(\"\\uD83D\\uDD27 CrewMultiSelectDropdown - selectedOptions after mapping:\", {\n                originalValueLength: value.length,\n                selectedOptionsLength: selectedOptions.length,\n                selectedOptions\n            });\n            setSelectedIDs(selectedOptions);\n        } else if (lodash_isEmpty__WEBPACK_IMPORTED_MODULE_2___default()(value)) {\n            console.log(\"\\uD83D\\uDD27 CrewMultiSelectDropdown - clearing selection (empty value)\");\n            // Clear selection when value is empty\n            setSelectedIDs([]);\n        }\n    }, [\n        value,\n        crewList\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_comboBox__WEBPACK_IMPORTED_MODULE_6__.Combobox, {\n                options: crewList,\n                value: selectedIDs,\n                onChange: handleOnChange,\n                placeholder: \"Select Crew\",\n                multi: true,\n                isLoading: isLoading || crewList.length === 0\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\multiselect-dropdown\\\\multiselect-dropdown.tsx\",\n                lineNumber: 441,\n                columnNumber: 13\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_add_crew_member_dialog__WEBPACK_IMPORTED_MODULE_7__.AddCrewMemberDialog, {\n                openDialog: openCreateMemberDialog,\n                setOpenDialog: setOpenCreateMemberDialog,\n                handleCreate: handleAddNewMember,\n                actionText: \"Add Crew Member\",\n                error: error\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\multiselect-dropdown\\\\multiselect-dropdown.tsx\",\n                lineNumber: 449,\n                columnNumber: 13\n            }, undefined)\n        ]\n    }, void 0, true);\n};\n_s(CrewMultiSelectDropdown, \"B5kTBW1ijKbUdQ1QBKs5KkaV5Fg=\", false, function() {\n    return [\n        _apollo_client__WEBPACK_IMPORTED_MODULE_9__.useLazyQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_10__.useMutation\n    ];\n});\n_c = CrewMultiSelectDropdown;\n/* harmony default export */ __webpack_exports__[\"default\"] = (CrewMultiSelectDropdown);\nvar _c;\n$RefreshReg$(_c, \"CrewMultiSelectDropdown\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/ui/crew/multiselect-dropdown/multiselect-dropdown.tsx\n"));

/***/ })

});